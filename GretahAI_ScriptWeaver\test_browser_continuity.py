"""
Integration Test for Browser State Continuity

This test validates the browser state continuity functionality
across the Stage 6→7→4 workflow cycle.

Test Scenarios:
1. Browser session creation and management
2. State preservation during script execution
3. Element selection with preserved state
4. Session cleanup and error handling
5. Integration with existing workflow components
"""

import pytest
import time
import tempfile
import os
from unittest.mock import Mock, patch

from core.browser_session_manager import BrowserSessionManager
from core.workflow_integration import WorkflowBrowserIntegration
from state_manager import StateManager, StateStage

class TestBrowserSessionManager:
    """Test the core browser session manager functionality."""
    
    def test_session_creation(self):
        """Test browser session creation."""
        manager = BrowserSessionManager()
        
        # Test session creation
        assert manager.create_session(headless=True)
        assert manager.is_active
        assert manager.session_id is not None
        assert manager.driver is not None
        
        # Cleanup
        manager.cleanup_session()
        assert not manager.is_active
        assert manager.driver is None
    
    def test_navigation(self):
        """Test navigation with session preservation."""
        manager = BrowserSessionManager()
        
        # Create session
        assert manager.create_session(headless=True)
        
        # Test navigation
        assert manager.navigate_to("https://example.com")
        assert manager.current_url == "https://example.com"
        
        # Verify page loaded
        session_info = manager.get_session_info()
        assert session_info['active']
        assert "example.com" in session_info['current_url']
        
        # Cleanup
        manager.cleanup_session()
    
    def test_session_info(self):
        """Test session information retrieval."""
        manager = BrowserSessionManager()
        
        # Test inactive session
        info = manager.get_session_info()
        assert not info['active']
        assert info['session_id'] is None
        
        # Create session and test active info
        assert manager.create_session(headless=True)
        info = manager.get_session_info()
        assert info['active']
        assert info['session_id'] is not None
        assert info['created_at'] is not None
        
        # Cleanup
        manager.cleanup_session()
    
    def test_session_timeout(self):
        """Test session timeout functionality."""
        manager = BrowserSessionManager()
        
        # Create session with short timeout
        assert manager.create_session(headless=True, timeout=2)
        assert manager.is_active
        
        # Wait for timeout
        time.sleep(3)
        
        # Session should be cleaned up
        assert not manager.is_active
    
    def test_session_extension(self):
        """Test session timeout extension."""
        manager = BrowserSessionManager()
        
        # Create session
        assert manager.create_session(headless=True, timeout=5)
        original_timeout = manager.session_timeout
        
        # Extend session
        assert manager.extend_session(10)
        assert manager.session_timeout == original_timeout + 10
        
        # Cleanup
        manager.cleanup_session()

class TestWorkflowIntegration:
    """Test workflow integration functionality."""
    
    def test_integration_initialization(self):
        """Test workflow integration initialization."""
        integration = WorkflowBrowserIntegration()
        state = Mock(spec=StateManager)
        state.website_url = "https://example.com"
        
        # Test initialization
        assert integration.initialize_session(state, headless=True)
        assert integration.is_session_active()
        
        # Test session info
        info = integration.get_session_info()
        assert info['active']
        
        # Cleanup
        integration.cleanup_session()
        assert not integration.is_session_active()
    
    def test_script_execution_with_continuity(self):
        """Test script execution with session preservation."""
        integration = WorkflowBrowserIntegration()
        state = Mock(spec=StateManager)
        state.website_url = "https://example.com"
        
        # Initialize session
        assert integration.initialize_session(state, headless=True)
        
        # Create temporary script file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write("""
# Test script
def test_example():
    print("Test script executed")
    return True

if __name__ == "__main__":
    test_example()
""")
            script_path = f.name
        
        try:
            # Execute script with continuity
            success, message, results = integration.execute_script_with_continuity(state, script_path)
            
            # Verify execution
            assert success
            assert "successfully" in message.lower()
            assert results.get('session_preserved')
            assert results.get('ready_for_element_selection')
            
            # Verify session is still active
            assert integration.is_session_active()
            
        finally:
            # Cleanup
            os.unlink(script_path)
            integration.cleanup_session()
    
    def test_transition_to_element_selection(self):
        """Test transition preparation for element selection."""
        integration = WorkflowBrowserIntegration()
        state = Mock(spec=StateManager)
        state.website_url = "https://example.com"
        
        # Initialize session
        assert integration.initialize_session(state, headless=True)
        
        # Test transition preparation
        assert integration.transition_to_element_selection(state)
        
        # Verify state attributes were set
        assert hasattr(state, 'browser_session_active')
        assert hasattr(state, 'session_ready_for_element_selection')
        assert state.browser_session_active
        assert state.session_ready_for_element_selection
        
        # Cleanup
        integration.cleanup_session()
    
    def test_error_handling(self):
        """Test error handling and recovery."""
        integration = WorkflowBrowserIntegration()
        
        # Test error handling without active session
        error = Exception("Test error")
        recovery_info = integration.handle_session_error(error)
        
        assert recovery_info['error_type'] == 'Exception'
        assert recovery_info['error_message'] == 'Test error'
        assert 'recovery_actions' in recovery_info
        assert not integration.integration_active

class TestEndToEndWorkflow:
    """Test end-to-end workflow scenarios."""
    
    def test_stage_6_7_4_cycle(self):
        """Test complete Stage 6→7→4 cycle with continuity."""
        integration = WorkflowBrowserIntegration()
        state = Mock(spec=StateManager)
        state.website_url = "https://example.com"
        state.current_stage = StateStage.STAGE6_GENERATE
        
        # Stage 6: Initialize session
        assert integration.initialize_session(state, headless=True)
        assert integration.is_session_active()
        
        # Stage 7: Execute script with continuity
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write("print('Test script')")
            script_path = f.name
        
        try:
            success, message, results = integration.execute_script_with_continuity(state, script_path)
            assert success
            assert results.get('session_preserved')
            
            # Stage 4: Prepare for element selection
            assert integration.transition_to_element_selection(state)
            assert integration.is_session_active()
            
            # Verify session is ready for element selection
            session_info = integration.get_session_info()
            assert session_info['active']
            
        finally:
            os.unlink(script_path)
            integration.cleanup_session()
    
    def test_multiple_cycles(self):
        """Test multiple workflow cycles with same session."""
        integration = WorkflowBrowserIntegration()
        state = Mock(spec=StateManager)
        state.website_url = "https://example.com"
        
        # Initialize session
        assert integration.initialize_session(state, headless=True)
        original_session_id = integration.session_manager.session_id
        
        # Run multiple cycles
        for i in range(3):
            # Create script
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(f"print('Cycle {i}')")
                script_path = f.name
            
            try:
                # Execute script
                success, _, _ = integration.execute_script_with_continuity(state, script_path)
                assert success
                
                # Prepare for element selection
                assert integration.transition_to_element_selection(state)
                
                # Verify same session is maintained
                assert integration.session_manager.session_id == original_session_id
                
            finally:
                os.unlink(script_path)
        
        # Cleanup
        integration.cleanup_session()

def run_manual_test():
    """
    Manual test function for interactive validation.
    
    This function can be run directly to test the browser continuity
    functionality in an interactive manner.
    """
    print("🔄 Starting Browser Continuity Manual Test")
    
    # Create integration instance
    integration = WorkflowBrowserIntegration()
    state = Mock(spec=StateManager)
    state.website_url = "https://example.com"
    
    try:
        # Step 1: Initialize session
        print("📝 Step 1: Initializing browser session...")
        if integration.initialize_session(state, headless=False):
            print("✅ Browser session created successfully")
        else:
            print("❌ Failed to create browser session")
            return
        
        # Step 2: Show session info
        print("\n📊 Step 2: Session information:")
        info = integration.get_session_info()
        print(f"  - Active: {info['active']}")
        print(f"  - URL: {info.get('current_url', 'N/A')}")
        print(f"  - Session ID: {info.get('session_id', 'N/A')[:8]}...")
        
        # Step 3: Simulate script execution
        print("\n🧪 Step 3: Simulating script execution...")
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write("print('Manual test script executed')")
            script_path = f.name
        
        success, message, results = integration.execute_script_with_continuity(state, script_path)
        if success:
            print("✅ Script executed with session preservation")
        else:
            print(f"❌ Script execution failed: {message}")
        
        os.unlink(script_path)
        
        # Step 4: Prepare for element selection
        print("\n🎯 Step 4: Preparing for element selection...")
        if integration.transition_to_element_selection(state):
            print("✅ Ready for element selection with preserved state")
        else:
            print("❌ Failed to prepare for element selection")
        
        # Step 5: Show final session state
        print("\n📊 Step 5: Final session state:")
        final_info = integration.get_session_info()
        print(f"  - Active: {final_info['active']}")
        print(f"  - Ready for selection: {final_info['active']}")
        
        print("\n🎉 Manual test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Manual test failed: {e}")
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        integration.cleanup_session()
        print("✅ Cleanup completed")

if __name__ == "__main__":
    # Run manual test if executed directly
    run_manual_test()
