"""
State Manager for GretahAI ScriptWeaver

This module provides a centralized state management class for the ScriptWeaver application.
It encapsulates all session state variables and provides helper methods for state mutations.

The StateManager follows a singleton pattern within the Streamlit session state:
1. A single instance is created and stored in st.session_state["state"]
2. All components access this shared instance via StateManager.get(st)
3. State mutations are performed directly on this instance

Key features:
- Organizes state by logical categories (metadata, counters, artifacts)
- Provides type hints for better IDE support and code safety
- Centralizes all state in one place to avoid scattered session_state access
- Includes helper methods for common state operations

Usage:
    # Initialize in main app
    StateManager().init_in_session(st)

    # Access in any function
    state = StateManager.get(st)

    # Mutate state directly
    state.current_step_index += 1
    state.test_data = {"username": "test_user"}

PHASE 1 CLEANUP COMPLETED (2,182 lines total):
===============================================

✅ REMOVED DEPRECATED METHODS (259 lines removed):
- update_stage_based_on_completion() - Deprecated (84 lines)
- current_app_stage property - Backward compatibility (12 lines)
- add_validation_feedback() - Unused analytics (38 lines)
- get_common_validation_issues() - Unused analytics (63 lines)
- track_script_regeneration() - Unused metrics (25 lines)
- get_feedback_effectiveness_metrics() - Unused analytics (55 lines)

✅ UPDATED USAGE SITES:
- app.py: Replaced current_app_stage with direct stage.get_stage_number()
- stage6.py: Replaced track_script_regeneration() with direct counter increment
- core/ai.py: Simplified analytics method calls
- core/prompt_builder.py: Simplified analytics method calls
- Test files: Updated to use advance_to() instead of deprecated methods

REMAINING METHODS (High Priority - Keep):
- init_in_session() - Core initialization
- get() - Static method for state access
- advance_to() - Stage transitions
- get_effective_step_table() - JSON-based step data
- update_step_progress() - Step counters
- reset_step_state() - Step cleanup
- reset_test_case_state() - Test case cleanup
- save_manual_script_edit() - Manual editing
- get_script_status_info() - Script status
- add_script_to_history() - Script tracking

NEXT PHASES (Modular Refactoring):
- Phase 2: Extract hybrid editing methods → core/hybrid_editing.py (~150 lines)
- Phase 3: Extract data persistence → core/step_data_persistence.py (~200 lines)
- Phase 4: Extract script management → core/script_management.py (~300 lines)
- Target final size: ~800-1000 lines
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
from enum import Enum


class StateStage(Enum):
    """
    Authoritative stage enumeration for GretahAI ScriptWeaver workflow.

    This enum serves as the single source of truth for stage determination,
    replacing the fragile distributed boolean flag interrogation system.
    """
    STAGE1_UPLOAD = "stage1_upload"  # CSV Upload
    STAGE2_WEBSITE = "stage2_website"  # Website Configuration
    STAGE3_CONVERT = "stage3_convert"  # Test Case Analysis and Conversion
    STAGE4_DETECT = "stage4_detect"  # UI Element Detection and Step Selection
    STAGE5_DATA = "stage5_data"  # Manual Data Entry
    STAGE6_GENERATE = "stage6_generate"  # Test Script Generation
    STAGE7_EXECUTE = "stage7_execute"  # Test Script Execution
    STAGE8_OPTIMIZE = "stage8_optimize"  # Script Consolidation and Optimization
    STAGE9_BROWSE = "stage9_browse"  # Script Browser and Comparison
    STAGE10_PLAYGROUND = "stage10_playground"  # Script Playground

    def __str__(self):
        return self.value

    def get_stage_number(self) -> int:
        """Get the numeric stage number (1-10)."""
        stage_map = {
            StateStage.STAGE1_UPLOAD: 1,
            StateStage.STAGE2_WEBSITE: 2,
            StateStage.STAGE3_CONVERT: 3,
            StateStage.STAGE4_DETECT: 4,
            StateStage.STAGE5_DATA: 5,
            StateStage.STAGE6_GENERATE: 6,
            StateStage.STAGE7_EXECUTE: 7,
            StateStage.STAGE8_OPTIMIZE: 8,
            StateStage.STAGE9_BROWSE: 9,
            StateStage.STAGE10_PLAYGROUND: 10
        }
        return stage_map.get(self, 1)

    def get_display_name(self) -> str:
        """Get the human-readable stage name."""
        stage_names = {
            StateStage.STAGE1_UPLOAD: "Stage 1: CSV Upload",
            StateStage.STAGE2_WEBSITE: "Stage 2: Website Configuration",
            StateStage.STAGE3_CONVERT: "Stage 3: Test Case Analysis and Conversion",
            StateStage.STAGE4_DETECT: "Stage 4: UI Element Detection and Step Selection",
            StateStage.STAGE5_DATA: "Stage 5: Manual Data Entry",
            StateStage.STAGE6_GENERATE: "Stage 6: Test Script Generation",
            StateStage.STAGE7_EXECUTE: "Stage 7: Test Script Execution",
            StateStage.STAGE8_OPTIMIZE: "Stage 8: Script Consolidation and Optimization",
            StateStage.STAGE9_BROWSE: "Stage 9: Script Browser and Comparison",
            StateStage.STAGE10_PLAYGROUND: "Stage 10: Script Playground"
        }
        return stage_names.get(self, "Unknown Stage")

    @classmethod
    def from_number(cls, stage_number: int) -> 'StateStage':
        """Create StateStage from numeric stage number (1-10)."""
        stage_map = {
            1: cls.STAGE1_UPLOAD,
            2: cls.STAGE2_WEBSITE,
            3: cls.STAGE3_CONVERT,
            4: cls.STAGE4_DETECT,
            5: cls.STAGE5_DATA,
            6: cls.STAGE6_GENERATE,
            7: cls.STAGE7_EXECUTE,
            8: cls.STAGE8_OPTIMIZE,
            9: cls.STAGE9_BROWSE,
            10: cls.STAGE10_PLAYGROUND
        }
        if stage_number not in stage_map:
            raise ValueError(f"Invalid stage number: {stage_number}. Must be 1-10.")
        return stage_map[stage_number]

    @classmethod
    def from_string(cls, stage_string: str) -> 'StateStage':
        """
        Create StateStage from string representation.

        This method handles both enum values and display names to ensure
        robust stage recovery from various string formats.

        Args:
            stage_string: String representation of the stage

        Returns:
            StateStage: The corresponding enum instance

        Raises:
            ValueError: If the string doesn't match any known stage
        """
        # Handle direct enum value matches
        for stage in cls:
            if stage.value == stage_string:
                return stage

        # Handle display name matches (case-insensitive)
        stage_string_lower = stage_string.lower()
        display_name_map = {
            "stage 1: csv upload": cls.STAGE1_UPLOAD,
            "stage 2: website configuration": cls.STAGE2_WEBSITE,
            "stage 3: test case analysis and conversion": cls.STAGE3_CONVERT,
            "stage 4: ui element detection and step selection": cls.STAGE4_DETECT,
            "stage 5: manual data entry": cls.STAGE5_DATA,
            "stage 6: test script generation": cls.STAGE6_GENERATE,
            "stage 7: test script execution": cls.STAGE7_EXECUTE,
            "stage 8: script consolidation and optimization": cls.STAGE8_OPTIMIZE,
            "stage 9: script browser and comparison": cls.STAGE9_BROWSE,
            "stage 10: script playground": cls.STAGE10_PLAYGROUND
        }

        if stage_string_lower in display_name_map:
            return display_name_map[stage_string_lower]

        # Handle partial matches for common patterns
        if "stage1" in stage_string_lower or "upload" in stage_string_lower:
            return cls.STAGE1_UPLOAD
        elif "stage2" in stage_string_lower or "website" in stage_string_lower:
            return cls.STAGE2_WEBSITE
        elif "stage3" in stage_string_lower or "convert" in stage_string_lower:
            return cls.STAGE3_CONVERT
        elif "stage4" in stage_string_lower or "detect" in stage_string_lower:
            return cls.STAGE4_DETECT
        elif "stage5" in stage_string_lower or "data" in stage_string_lower:
            return cls.STAGE5_DATA
        elif "stage6" in stage_string_lower or "generate" in stage_string_lower:
            return cls.STAGE6_GENERATE
        elif "stage7" in stage_string_lower or "execute" in stage_string_lower:
            return cls.STAGE7_EXECUTE
        elif "stage8" in stage_string_lower or "optimize" in stage_string_lower:
            return cls.STAGE8_OPTIMIZE
        elif "stage9" in stage_string_lower or "browse" in stage_string_lower:
            return cls.STAGE9_BROWSE
        elif "stage10" in stage_string_lower or "playground" in stage_string_lower:
            return cls.STAGE10_PLAYGROUND

        raise ValueError(f"Unknown stage string: {stage_string}")

    @classmethod
    def safe_from_any(cls, stage_input) -> 'StateStage':
        """
        Safely create StateStage from any input type.

        This method provides robust stage recovery by handling:
        - StateStage enum instances (return as-is)
        - String representations (via from_string)
        - Numeric values (via from_number)
        - Invalid inputs (fallback to STAGE1_UPLOAD)

        Args:
            stage_input: Any input that might represent a stage

        Returns:
            StateStage: The corresponding enum instance or STAGE1_UPLOAD as fallback
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Already a StateStage enum
        if isinstance(stage_input, cls):
            return stage_input

        # String input
        if isinstance(stage_input, str):
            try:
                return cls.from_string(stage_input)
            except ValueError as e:
                logger.warning(f"Failed to parse stage string '{stage_input}': {e}")
                logger.warning("Falling back to Stage 1")
                return cls.STAGE1_UPLOAD

        # Numeric input
        if isinstance(stage_input, (int, float)):
            try:
                return cls.from_number(int(stage_input))
            except ValueError as e:
                logger.warning(f"Failed to parse stage number '{stage_input}': {e}")
                logger.warning("Falling back to Stage 1")
                return cls.STAGE1_UPLOAD

        # Unknown input type
        logger.warning(f"Unknown stage input type: {type(stage_input)} = {stage_input}")
        logger.warning("Falling back to Stage 1")
        return cls.STAGE1_UPLOAD

@dataclass
class StateManager:
    """
    Centralized state manager for the ScriptWeaver application.

    This class stores all application state in a structured way, organized by logical categories.
    It uses Python's dataclass for clean definition and type hints for better IDE support.

    The state is organized into these categories:
    1. Core test-run metadata - Basic information about the test case and selected steps
    2. Step-progress counters - Track progress through multi-step test cases
    3. Per-step artifacts - Data generated for each step (scripts, analysis, etc.)
    4. Browser and element detection - UI elements and browser instances
    5. Flags - Boolean indicators for application flow control
    6. Usage tracking - Metrics for API usage and performance

    All state mutations should be performed directly on the StateManager instance
    retrieved via StateManager.get(st).
    """

    # ───── Centralized Stage Management ─────
    current_stage: StateStage = StateStage.STAGE1_UPLOAD  # Authoritative current stage (single source of truth)

    def __post_init__(self):
        """
        Post-initialization to ensure stage consistency.

        This method ensures that the current_stage is always a proper StateStage enum,
        even if it was loaded from session state as a string or other format.
        """
        # Ensure current_stage is always a proper StateStage enum
        if not isinstance(self.current_stage, StateStage):
            self.current_stage = StateStage.safe_from_any(self.current_stage)

    # ───── Core test-run metadata ─────
    uploaded_excel: Optional[str] = None  # Path to the uploaded Excel file
    uploaded_file: Optional[str] = None  # Alias for uploaded_excel (for backward compatibility)
    last_file_content_hash: Optional[int] = None  # Hash of the last processed file content
    test_cases: Optional[List[Dict[str, Any]]] = None  # All test cases from Excel
    selected_test_case: Optional[Dict[str, Any]] = None  # Currently selected test case
    original_test_case: Optional[Dict[str, Any]] = None  # Original unmodified test case
    google_api_key: Optional[str] = None  # Google AI API key
    selected_step: Optional[Dict[str, Any]] = None  # Currently selected test step
    selected_step_table_entry: Optional[Dict[str, Any]] = None  # Step in automation-ready format
    step_elements: List[Dict[str, Any]] = field(default_factory=list)  # UI elements for current step
    website_url: Optional[str] = None  # Target website URL

    # ───── Step-progress counters ─────
    current_step_index: int = 0  # Index of current step in step_table_json
    total_steps: int = 0  # Total number of steps in the test case
    all_steps_done: bool = False  # Flag indicating all steps are processed
    completed_steps: List[str] = field(default_factory=list)  # List of completed step numbers
    step_context: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # Context from previous steps

    # ───── Per-step artifacts ─────
    step_table_markdown: str = ""  # Markdown representation of step table
    step_table_json: List[Dict[str, Any]] = field(default_factory=list)  # JSON representation of step table
    step_table_analysis: Optional[Dict[str, Any]] = None  # Analysis of step table

    # ───── Hybrid editing system for AI + manual steps ─────
    ai_generated_steps: Optional[List[Dict[str, Any]]] = None  # Original AI-generated steps (locked)
    manual_steps: List[Dict[str, Any]] = field(default_factory=list)  # User-added manual steps
    step_insertion_points: Dict[str, List[Dict[str, Any]]] = field(default_factory=dict)  # Manual steps by insertion point
    hybrid_editing_enabled: bool = False  # Flag to enable hybrid editing mode
    combined_step_table: Optional[List[Dict[str, Any]]] = None  # Final combined AI + manual steps
    completeness_validation: Optional[Dict[str, Any]] = None  # AI completeness validation results
    ui_elements: List[Dict[str, Any]] = field(default_factory=list)  # All UI elements
    element_matches: Dict[str, Any] = field(default_factory=dict)  # Matched elements for steps
    step_matches: Dict[str, Any] = field(default_factory=dict)  # Alias for element_matches
    test_data: Dict[str, Any] = field(default_factory=dict)  # Test data for current step
    manual_test_data: Dict[str, Any] = field(default_factory=dict)  # Manually entered test data
    test_data_skipped: bool = False  # Flag indicating test data was skipped
    llm_step_analysis: Dict[str, Any] = field(default_factory=dict)  # LLM analysis of current step
    test_data_analysis: Dict[str, Any] = field(default_factory=dict)  # Analysis of test data requirements
    generated_script_path: Optional[str] = None  # Path to generated test script
    last_script_content: str = ""  # Content of last generated script
    last_script_file: str = ""  # Path to last generated script file
    test_results: Optional[Dict[str, Any]] = None  # Results of test execution
    script_validation_results: Dict[str, Any] = field(default_factory=dict)  # Results from script validation
    validation_feedback_history: List[Dict[str, Any]] = field(default_factory=list)  # Historical validation feedback for learning
    script_regeneration_count: int = field(default=0)  # Track regeneration attempts for feedback loop analysis
    regen_attempts: int = 0  # Track current regeneration attempts for limiting infinite loops

    # ───── Stage 8 optimization artifacts ─────
    optimized_script_path: Optional[str] = None  # Path to optimized script from Stage 8
    optimized_script_content: str = ""  # Content of optimized script
    optimization_in_progress: bool = False  # Flag indicating optimization is in progress
    optimization_complete: bool = False  # Flag indicating optimization is complete
    optimization_start_time: Optional[datetime] = None  # Timestamp when optimization started
    optimization_chunks: List[Dict[str, Any]] = field(default_factory=list)  # Chunks for large script optimization
    optimized_script_test_results: Optional[Dict[str, Any]] = None  # Test results for optimized script
    combined_script_path: Optional[str] = None  # Path to combined script file

    # ───── Stage 6 regeneration and comment enhancement ─────
    user_generation_comment: str = ""  # User's raw comment/feedback for script generation improvement
    ai_enhanced_generation_comment: str = ""  # AI-enhanced version of user's generation comment
    generation_comment_enhancement_done: bool = False  # Flag indicating AI generation comment enhancement is complete
    use_enhanced_generation_comment: bool = False  # Flag indicating user chose to use AI-enhanced generation comment
    generation_custom_instructions: Optional[str] = None  # Custom instructions for script generation regeneration

    # ───── Stage 8 validation and comment enhancement ─────
    optimized_script_validation_results: Dict[str, Any] = field(default_factory=dict)  # Validation results for optimized script
    optimized_script_validation_done: bool = False  # Flag indicating optimized script validation is complete
    user_optimization_comment: str = ""  # User's raw comment/feedback for script improvement
    ai_enhanced_comment: str = ""  # AI-enhanced version of user's comment
    comment_enhancement_done: bool = False  # Flag indicating AI comment enhancement is complete
    use_enhanced_comment: bool = False  # Flag indicating user chose to use AI-enhanced comment
    regeneration_custom_instructions: Optional[str] = None  # Custom instructions for script regeneration

    # ───── Script continuity tracking ─────
    combined_script_content: Optional[str] = None  # Combined script content for all steps
    script_imports: List[str] = field(default_factory=list)  # List of import statements
    script_fixtures: List[str] = field(default_factory=list)  # List of fixture definitions
    script_variables: Dict[str, str] = field(default_factory=dict)  # Shared variables between steps
    script_functions: Dict[str, str] = field(default_factory=dict)  # Helper functions defined in scripts
    browser_initialized: bool = False  # Flag indicating browser has been initialized
    previous_scripts: Dict[str, str] = field(default_factory=dict)  # Dictionary of previous scripts by step number

    # ───── Manual script editing ─────
    script_manually_edited: bool = False  # Flag indicating if current script was manually edited
    original_ai_script_content: str = ""  # Original AI-generated script content for revert functionality
    manual_edit_timestamp: Optional[datetime] = None  # Timestamp when manual edit was made
    script_edit_mode: bool = False  # Flag indicating if script editor is in edit mode

    # ───── Script history tracking (Stage 9) ─────
    script_history: List[Dict[str, Any]] = field(default_factory=list)  # Complete history of all generated scripts
    script_metadata: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # Metadata for each script version
    _script_storage: Any = field(default=None, init=False)  # Persistent storage instance

    # ───── Browser and element detection ─────
    test_browser: Any = None  # Browser instance for testing
    detected_elements: List[Dict[str, Any]] = field(default_factory=list)  # All detected UI elements
    qa_relevant_elements: List[Dict[str, Any]] = field(default_factory=list)  # Filtered QA-relevant elements

    # ───── Browser continuity state ─────
    browser_session_active: bool = False  # Flag indicating browser session is active for continuity
    session_ready_for_element_selection: bool = False  # Flag indicating session is ready for element selection
    browser_continuity_enabled: bool = False  # Flag indicating browser continuity is enabled
    last_browser_session_url: Optional[str] = None  # Last URL accessed in browser session
    browser_session_created_at: Optional[datetime] = None  # Timestamp when browser session was created

    # ───── Flags (use one per concept only) ─────
    conversion_done: bool = False  # Flag indicating test case conversion is complete
    converted_test_case_id: Optional[str] = None  # ID of the test case that was converted (for synchronization)
    step_ready_for_script: bool = False  # Flag indicating step is ready for script generation
    script_just_generated: bool = False  # Flag indicating script was just generated
    use_ai_matching: bool = True  # Flag indicating whether to use AI for element matching
    script_validation_done: bool = False  # Flag indicating script validation is complete
    script_validation_passed: bool = False  # Flag indicating script validation passed

    # ───── Error handling and workflow control ─────
    execution_error_occurred: bool = False  # Flag indicating script execution failed
    execution_error_acknowledged: bool = False  # Flag indicating user acknowledged the error
    execution_error_details: Dict[str, Any] = field(default_factory=dict)  # Error details for display

    # ───── Google AI Studio usage tracking ─────
    google_request_timestamps: List[datetime] = field(default_factory=list)  # Timestamps of API requests
    google_token_usage: List[Any] = field(default_factory=list)  # Token usage per request

    # ───── Helper methods ─────
    def init_in_session(self, st):
        """
        Initialize the state manager in the Streamlit session state.

        This method stores the StateManager instance in st.session_state["state"]
        if it doesn't already exist. This ensures we have a single source of truth
        for application state.

        Args:
            st: The Streamlit module instance
        """
        if "state" not in st.session_state:
            st.session_state["state"] = self  # persist
            # Initialize persistent storage
            self._init_script_storage()
        else:
            # Check if existing state needs upgrades
            existing_state = st.session_state["state"]
            # CRITICAL FIX: Add protection flag if missing
            if not hasattr(existing_state, '_session_state_protected'):
                existing_state._session_state_protected = True

            needs_upgrade = (
                not hasattr(existing_state, 'set_execution_error') or
                not hasattr(existing_state, 'current_stage') or
                not hasattr(existing_state, '_script_storage') or
                not hasattr(existing_state, 'clear_all_script_history') or
                not hasattr(existing_state, 'get_all_scripts_with_history') or
                not hasattr(existing_state, 'add_script_to_history') or
                not hasattr(existing_state, 'script_manually_edited') or
                not hasattr(existing_state, 'save_manual_script_edit') or
                not hasattr(existing_state, 'revert_to_ai_script') or
                not hasattr(existing_state, 'get_script_status_info') or
                not hasattr(existing_state, '_determine_stage_from_state')
            )
            if needs_upgrade:
                # Update existing state with new fields and methods
                self._upgrade_existing_state(existing_state)
                # Initialize persistent storage if not present
                if not hasattr(existing_state, '_script_storage') or existing_state._script_storage is None:
                    existing_state._init_script_storage()

    def _upgrade_existing_state(self, existing_state):
        """
        Upgrade an existing StateManager instance with new capabilities.

        Args:
            existing_state: The existing StateManager instance to upgrade
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Add centralized stage management if it doesn't exist
        if not hasattr(existing_state, 'current_stage'):
            logger.info("CRITICAL FIX: Adding current_stage to existing state")
            # CRITICAL FIX: Determine correct stage BEFORE setting current_stage
            # This prevents the race condition that causes reversion to Stage 1

            # Analyze existing state to determine the correct stage
            # CRITICAL FIX: Call the method directly since existing_state doesn't have it yet
            logger.info("CRITICAL FIX: Calling _analyze_state_for_stage")
            determined_stage = self._analyze_state_for_stage(existing_state)
            logger.info(f"CRITICAL FIX: Determined stage: {determined_stage}")

            # Set the determined stage directly (no intermediate Stage 1 assignment)
            existing_state.current_stage = determined_stage
            logger.info(f"Added current_stage field to existing state: {existing_state.current_stage.get_display_name()}")
            logger.info("CRITICAL FIX: Stage determined without intermediate Stage 1 assignment")
        else:
            logger.info("CRITICAL FIX: existing_state already has current_stage")

        # Add new error handling fields if they don't exist
        if not hasattr(existing_state, 'execution_error_occurred'):
            existing_state.execution_error_occurred = False
            logger.info("Added execution_error_occurred field to existing state")

        if not hasattr(existing_state, 'execution_error_acknowledged'):
            existing_state.execution_error_acknowledged = False
            logger.info("Added execution_error_acknowledged field to existing state")

        if not hasattr(existing_state, 'execution_error_details'):
            existing_state.execution_error_details = {}
            logger.info("Added execution_error_details field to existing state")

        # Add new methods to existing state
        existing_state.set_execution_error = self.set_execution_error.__get__(existing_state, StateManager)
        existing_state.acknowledge_execution_error = self.acknowledge_execution_error.__get__(existing_state, StateManager)
        existing_state.clear_execution_error = self.clear_execution_error.__get__(existing_state, StateManager)

        # Add script history management methods if they don't exist
        if not hasattr(existing_state, 'clear_all_script_history'):
            existing_state.clear_all_script_history = self.clear_all_script_history.__get__(existing_state, StateManager)
            logger.info("Added clear_all_script_history method to existing state")

        if not hasattr(existing_state, 'get_all_scripts_with_history'):
            existing_state.get_all_scripts_with_history = self.get_all_scripts_with_history.__get__(existing_state, StateManager)
            logger.info("Added get_all_scripts_with_history method to existing state")

        if not hasattr(existing_state, 'add_script_to_history'):
            existing_state.add_script_to_history = self.add_script_to_history.__get__(existing_state, StateManager)
            logger.info("Added add_script_to_history method to existing state")

        if not hasattr(existing_state, '_load_historical_scripts'):
            existing_state._load_historical_scripts = self._load_historical_scripts.__get__(existing_state, StateManager)
            logger.info("Added _load_historical_scripts method to existing state")

        if not hasattr(existing_state, '_init_script_storage'):
            existing_state._init_script_storage = self._init_script_storage.__get__(existing_state, StateManager)
            logger.info("Added _init_script_storage method to existing state")

        # Add script history fields if they don't exist
        if not hasattr(existing_state, 'script_history'):
            existing_state.script_history = []
            logger.info("Added script_history field to existing state")

        if not hasattr(existing_state, 'script_metadata'):
            existing_state.script_metadata = {}
            logger.info("Added script_metadata field to existing state")

        # Add conversion tracking field if it doesn't exist
        if not hasattr(existing_state, 'converted_test_case_id'):
            existing_state.converted_test_case_id = None
            logger.info("Added converted_test_case_id field to existing state")

        # Add hybrid editing fields if they don't exist
        if not hasattr(existing_state, 'ai_generated_steps'):
            existing_state.ai_generated_steps = None
            logger.info("Added ai_generated_steps field to existing state")

        if not hasattr(existing_state, 'manual_steps'):
            existing_state.manual_steps = []
            logger.info("Added manual_steps field to existing state")

        if not hasattr(existing_state, 'step_insertion_points'):
            existing_state.step_insertion_points = {}
            logger.info("Added step_insertion_points field to existing state")

        if not hasattr(existing_state, 'hybrid_editing_enabled'):
            existing_state.hybrid_editing_enabled = False
            logger.info("Added hybrid_editing_enabled field to existing state")

        if not hasattr(existing_state, 'combined_step_table'):
            existing_state.combined_step_table = None
            logger.info("Added combined_step_table field to existing state")

        # Add hybrid editing methods
        existing_state.enable_hybrid_editing = self.enable_hybrid_editing.__get__(existing_state, StateManager)
        existing_state.disable_hybrid_editing = self.disable_hybrid_editing.__get__(existing_state, StateManager)
        existing_state.add_manual_step = self.add_manual_step.__get__(existing_state, StateManager)
        existing_state.remove_manual_step = self.remove_manual_step.__get__(existing_state, StateManager)
        existing_state.get_combined_steps = self.get_combined_steps.__get__(existing_state, StateManager)
        existing_state.get_effective_step_table = self.get_effective_step_table.__get__(existing_state, StateManager)
        existing_state.get_effective_total_steps = self.get_effective_total_steps.__get__(existing_state, StateManager)
        existing_state.sync_step_table_with_combined = self.sync_step_table_with_combined.__get__(existing_state, StateManager)

        # CRITICAL FIX: Add the new stage determination method
        if not hasattr(existing_state, '_determine_stage_from_state'):
            existing_state._determine_stage_from_state = self._determine_stage_from_state.__get__(existing_state, StateManager)
            logger.info("Added _determine_stage_from_state method to existing state")

        logger.info("Successfully upgraded existing StateManager with new capabilities")

    def _determine_stage_from_state(self, state_obj) -> StateStage:
        """
        Determine the correct stage based on existing state without causing reversion.

        This method analyzes the state object to determine the appropriate stage
        without triggering the problematic update_stage_based_on_completion logic.

        Args:
            state_obj: The state object to analyze

        Returns:
            StateStage: The determined stage
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Start with Stage 1 as baseline
        target_stage = StateStage.STAGE1_UPLOAD

        # Check Stage 1 completion (file uploaded)
        if (hasattr(state_obj, 'uploaded_excel') and state_obj.uploaded_excel) or \
           (hasattr(state_obj, 'uploaded_file') and state_obj.uploaded_file) or \
           (hasattr(state_obj, 'test_cases') and state_obj.test_cases):
            target_stage = StateStage.STAGE2_WEBSITE

            # Check Stage 2 completion (website URL configured)
            if hasattr(state_obj, 'website_url') and state_obj.website_url and \
               state_obj.website_url != "https://example.com":
                target_stage = StateStage.STAGE3_CONVERT

                # Check Stage 3 completion (test case selected and converted)
                if hasattr(state_obj, 'selected_test_case') and state_obj.selected_test_case and \
                   hasattr(state_obj, 'conversion_done') and state_obj.conversion_done and \
                   hasattr(state_obj, 'step_table_json') and state_obj.step_table_json:
                    target_stage = StateStage.STAGE4_DETECT

                    # Check Stage 4 completion (step selected and elements matched)
                    if hasattr(state_obj, 'selected_step') and state_obj.selected_step and \
                       (hasattr(state_obj, 'step_matches') and state_obj.step_matches or \
                        hasattr(state_obj, 'element_matches') and state_obj.element_matches):
                        target_stage = StateStage.STAGE5_DATA

                        # Check Stage 5 completion (test data configured or skipped)
                        if (hasattr(state_obj, 'test_data') and state_obj.test_data) or \
                           (hasattr(state_obj, 'test_data_skipped') and state_obj.test_data_skipped):
                            target_stage = StateStage.STAGE6_GENERATE

                            # Check Stage 6 completion (script generated)
                            if hasattr(state_obj, 'generated_script_path') and state_obj.generated_script_path:
                                target_stage = StateStage.STAGE7_EXECUTE

                                # Check Stage 7 completion (all steps done)
                                if hasattr(state_obj, 'all_steps_done') and state_obj.all_steps_done:
                                    target_stage = StateStage.STAGE8_OPTIMIZE

        logger.info(f"Determined stage from existing state: {target_stage.get_display_name()}")
        return target_stage

    def _analyze_state_for_stage(self, state_obj) -> StateStage:
        """
        Analyze state object to determine appropriate stage (helper for upgrade logic).

        This is a direct implementation that doesn't rely on methods that might not exist yet.

        Args:
            state_obj: The state object to analyze

        Returns:
            StateStage: The determined stage
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Start with Stage 1 as baseline
        target_stage = StateStage.STAGE1_UPLOAD

        # Debug logging
        logger.info(f"_analyze_state_for_stage: Starting analysis")
        logger.info(f"  uploaded_excel: {getattr(state_obj, 'uploaded_excel', None)}")
        logger.info(f"  test_cases: {bool(getattr(state_obj, 'test_cases', None))}")
        logger.info(f"  website_url: {getattr(state_obj, 'website_url', None)}")

        # Check Stage 1 completion (file uploaded)
        has_file = (hasattr(state_obj, 'uploaded_excel') and state_obj.uploaded_excel) or \
                   (hasattr(state_obj, 'uploaded_file') and state_obj.uploaded_file) or \
                   (hasattr(state_obj, 'test_cases') and state_obj.test_cases)

        if has_file:
            target_stage = StateStage.STAGE2_WEBSITE
            logger.info(f"  → Stage 1 complete, advancing to Stage 2")

            # Check Stage 2 completion (website URL configured)
            has_website = hasattr(state_obj, 'website_url') and state_obj.website_url and \
                         state_obj.website_url != "https://example.com"

            if has_website:
                target_stage = StateStage.STAGE3_CONVERT
                logger.info(f"  → Stage 2 complete, advancing to Stage 3")

                # Check Stage 3 completion (test case selected and converted)
                has_conversion = hasattr(state_obj, 'selected_test_case') and state_obj.selected_test_case and \
                               hasattr(state_obj, 'conversion_done') and state_obj.conversion_done and \
                               hasattr(state_obj, 'step_table_json') and state_obj.step_table_json

                if has_conversion:
                    target_stage = StateStage.STAGE4_DETECT
                    logger.info(f"  → Stage 3 complete, advancing to Stage 4")

                    # Check Stage 4 completion (step selected and elements matched)
                    has_step_selection = hasattr(state_obj, 'selected_step') and state_obj.selected_step and \
                                       (hasattr(state_obj, 'step_matches') and state_obj.step_matches or \
                                        hasattr(state_obj, 'element_matches') and state_obj.element_matches)

                    if has_step_selection:
                        target_stage = StateStage.STAGE5_DATA
                        logger.info(f"  → Stage 4 complete, advancing to Stage 5")

                        # Check Stage 5 completion (test data configured or skipped)
                        has_test_data = (hasattr(state_obj, 'test_data') and state_obj.test_data) or \
                                       (hasattr(state_obj, 'test_data_skipped') and state_obj.test_data_skipped)

                        if has_test_data:
                            target_stage = StateStage.STAGE6_GENERATE
                            logger.info(f"  → Stage 5 complete, advancing to Stage 6")

                            # Check Stage 6 completion (script generated)
                            has_script = hasattr(state_obj, 'generated_script_path') and state_obj.generated_script_path

                            if has_script:
                                target_stage = StateStage.STAGE7_EXECUTE
                                logger.info(f"  → Stage 6 complete, advancing to Stage 7")

                                # Check Stage 7 completion (all steps done)
                                all_done = hasattr(state_obj, 'all_steps_done') and state_obj.all_steps_done

                                if all_done:
                                    target_stage = StateStage.STAGE8_OPTIMIZE
                                    logger.info(f"  → Stage 7 complete, advancing to Stage 8")

        logger.info(f"_analyze_state_for_stage: Final determination: {target_stage.get_display_name()}")
        return target_stage

    @staticmethod
    def get(st) -> "StateManager":
        """
        Get the state manager from the Streamlit session state.

        This static method retrieves the StateManager instance from the
        Streamlit session state. It should be used by all components that
        need to access or modify application state.

        Args:
            st: The Streamlit module instance

        Returns:
            StateManager: The singleton StateManager instance
        """
        return st.session_state["state"]

    def update_step_progress(self, current_step_index=None, total_steps=None, all_steps_done=None,
                           step_ready_for_script=None, script_just_generated=None):
        """
        Update step progress counters with validation and logging.

        This method provides a centralized way to update step progress counters,
        ensuring that all updates are properly validated and logged.

        Args:
            current_step_index: New value for current_step_index
            total_steps: New value for total_steps
            all_steps_done: New value for all_steps_done
            step_ready_for_script: New value for step_ready_for_script
            script_just_generated: New value for script_just_generated

        Returns:
            bool: True if any state was updated, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        updated = False

        if current_step_index is not None and current_step_index != self.current_step_index:
            # Validate the new step index
            if total_steps is not None:
                max_index = total_steps - 1
            else:
                max_index = self.total_steps - 1 if self.total_steps > 0 else 0

            if current_step_index < 0 or (max_index > 0 and current_step_index > max_index):
                logger.warning(f"Invalid step index: {current_step_index}, valid range is 0-{max_index}")
                # Clamp to valid range
                current_step_index = max(0, min(current_step_index, max_index))

            if current_step_index != self.current_step_index:
                logger.info(f"State change: current_step_index: {self.current_step_index} -> {current_step_index}")
                self.current_step_index = current_step_index
                updated = True

        if total_steps is not None:
            if total_steps != self.total_steps:
                logger.info(f"State change: total_steps: {self.total_steps} -> {total_steps}")
                self.total_steps = total_steps
                updated = True

        if all_steps_done is not None:
            if all_steps_done != self.all_steps_done:
                logger.info(f"State change: all_steps_done: {self.all_steps_done} -> {all_steps_done}")
                self.all_steps_done = all_steps_done
                updated = True

        if step_ready_for_script is not None:
            if step_ready_for_script != self.step_ready_for_script:
                logger.info(f"State change: step_ready_for_script: {self.step_ready_for_script} -> {step_ready_for_script}")
                self.step_ready_for_script = step_ready_for_script
                updated = True

        if script_just_generated is not None:
            if script_just_generated != self.script_just_generated:
                logger.info(f"State change: script_just_generated: {self.script_just_generated} -> {script_just_generated}")
                self.script_just_generated = script_just_generated
                updated = True

        return updated

    def reset_step_state(self, confirm=False, reason=""):
        """
        Reset step-specific state variables.

        This method provides a centralized way to reset step-specific state variables,
        ensuring that all resets are properly validated, logged, and confirmed by the user.

        Args:
            confirm: Whether the reset has been confirmed by the user
            reason: The reason for the reset (for logging)

        Returns:
            bool: True if state was reset, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not confirm:
            logger.warning(f"Attempted to reset step state without confirmation: {reason}")
            return False

        logger.info(f"Resetting step state: {reason}")

        # Reset step-specific state variables
        self.step_elements = []
        self.step_matches = {}
        self.element_matches = {}
        self.test_data = {}
        self.manual_test_data = {}  # Reset manually entered test data
        self.test_data_skipped = False
        self.llm_step_analysis = {}
        self.step_ready_for_script = False
        self.script_just_generated = False
        self.generated_script_path = None

        # Reset validation state
        self.script_validation_done = False
        self.script_validation_passed = False
        self.script_validation_results = {}

        # Reset regeneration attempts counter
        self.regen_attempts = 0

        # Reset error state
        self.execution_error_occurred = False
        self.execution_error_acknowledged = False
        self.execution_error_details = {}

        return True

    def reset_test_case_state(self, confirm=False, reason=""):
        """
        Reset test case state variables.

        This method provides a centralized way to reset test case state variables,
        ensuring that all resets are properly validated, logged, and confirmed by the user.

        Args:
            confirm: Whether the reset has been confirmed by the user
            reason: The reason for the reset (for logging)

        Returns:
            bool: True if state was reset, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not confirm:
            logger.warning(f"Attempted to reset test case state without confirmation: {reason}")
            return False

        logger.info(f"Resetting test case state: {reason}")

        # Reset test case state variables
        self.selected_test_case = None
        self.original_test_case = None
        self.selected_step = None  # Clear selected step as part of test case reset
        self.selected_step_table_entry = None  # Clear step table entry as well
        self.step_table_markdown = ""
        self.step_table_json = []
        self.step_table_analysis = None
        self.conversion_done = False
        self.converted_test_case_id = None  # Clear conversion tracking

        # Also reset step state
        self.reset_step_state(confirm=True, reason=f"Part of test case reset: {reason}")

        # Reset step progression counters
        self.current_step_index = 0
        self.total_steps = 0
        self.all_steps_done = False
        self.completed_steps = []
        self.step_context = {}

        # Reset script continuity tracking
        self.combined_script_content = None
        self.combined_script_path = None
        self.script_imports = []
        self.script_fixtures = []
        self.script_variables = {}
        self.script_functions = {}
        self.browser_initialized = False
        self.previous_scripts = {}

        # Reset manual script editing state
        self.script_manually_edited = False
        self.original_ai_script_content = ""
        self.manual_edit_timestamp = None
        self.script_edit_mode = False

        # Reset Stage 8 optimization state
        self.optimized_script_path = None
        self.optimized_script_content = ""
        self.optimization_in_progress = False
        self.optimization_complete = False
        self.optimization_start_time = None
        self.optimization_chunks = []
        self.optimized_script_test_results = None  # Test results for optimized script

        # Reset Stage 6 regeneration and comment enhancement state
        self.user_generation_comment = ""
        self.ai_enhanced_generation_comment = ""
        self.generation_comment_enhancement_done = False
        self.use_enhanced_generation_comment = False
        self.generation_custom_instructions = None

        # Reset Stage 8 validation and comment enhancement state
        self.optimized_script_validation_results = {}
        self.optimized_script_validation_done = False
        self.user_optimization_comment = ""
        self.ai_enhanced_comment = ""
        self.comment_enhancement_done = False
        self.use_enhanced_comment = False
        self.regeneration_custom_instructions = None

        # Reset current stage to Stage 3 (test case selection) using centralized method
        self.advance_to(StateStage.STAGE3_CONVERT, f"Test case reset: {reason}")

        return True

    def enable_hybrid_editing(self) -> bool:
        """
        Enable hybrid editing mode for the current test case.

        Returns:
            bool: True if hybrid editing was enabled successfully
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not self.step_table_json:
            logger.warning("Cannot enable hybrid editing: no step table JSON available")
            return False

        self.hybrid_editing_enabled = True
        self.ai_generated_steps = self.step_table_json.copy()

        # Mark AI steps as locked
        for step in self.ai_generated_steps:
            step["_is_ai_generated"] = True
            step["_is_locked"] = True

        logger.info("Enabled hybrid editing mode")
        return True

    def disable_hybrid_editing(self):
        """Disable hybrid editing mode and clear manual steps."""
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        self.hybrid_editing_enabled = False
        self.step_insertion_points.clear()
        self.combined_step_table = None
        self.ai_generated_steps = None
        logger.info("Disabled hybrid editing mode")

    def add_manual_step(self, step: Dict[str, Any], insertion_point: str = "end"):
        """
        Add a manual step at the specified insertion point.

        Args:
            step: Manual step data
            insertion_point: Where to insert ("before_X", "after_X", "start", "end")
        """
        import logging
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        step["_is_manual"] = True
        step["_is_locked"] = False
        step["_insertion_point"] = insertion_point
        step["_created_at"] = datetime.now().isoformat()

        if insertion_point not in self.step_insertion_points:
            self.step_insertion_points[insertion_point] = []

        self.step_insertion_points[insertion_point].append(step)
        logger.info(f"Added manual step at insertion point: {insertion_point}")

    def remove_manual_step(self, step_id: str, insertion_point: str):
        """Remove a manual step from the specified insertion point."""
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if insertion_point in self.step_insertion_points:
            self.step_insertion_points[insertion_point] = [
                step for step in self.step_insertion_points[insertion_point]
                if step.get("_step_id") != step_id
            ]
            logger.info(f"Removed manual step {step_id} from insertion point: {insertion_point}")

    def get_combined_steps(self) -> Optional[List[Dict[str, Any]]]:
        """
        Get the combined AI and manual steps.

        Returns:
            List of combined steps or None if hybrid editing is not enabled
        """
        if not self.hybrid_editing_enabled or not self.ai_generated_steps:
            return None

        from core.step_merger import merge_ai_and_manual_steps

        merged_steps, _, _ = merge_ai_and_manual_steps(
            self.ai_generated_steps,
            self.step_insertion_points
        )

        return merged_steps

    def get_effective_step_table(self) -> List[Dict[str, Any]]:
        """
        Get the effective step table from persistent JSON storage ONLY.

        This method enforces JSON-only data loading as the single source of truth.
        No fallbacks to in-memory state are allowed.

        Returns:
            List of steps from JSON storage

        Raises:
            ValueError: If no JSON data exists for the current test case
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info("=== get_effective_step_table() called (JSON-ONLY MODE) ===")

        # Ensure we have a selected test case
        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            error_msg = "No test case selected - cannot load step data"
            logger.error(error_msg)
            raise ValueError(error_msg)

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            error_msg = "Selected test case has no ID - cannot load step data"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # Load from persistent JSON storage (ONLY source of truth)
        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()
            json_data = storage.load_step_data(test_case_id)

            if json_data:
                step_data, metadata = json_data
                logger.info(f"✅ Loaded step data from JSON (count: {len(step_data)})")
                logger.info(f"  → Test Case: {test_case_id}")
                logger.info(f"  → Source: {metadata.get('source', 'unknown')}")
                logger.info(f"  → Timestamp: {metadata.get('save_timestamp', 'unknown')}")

                if step_data:
                    first_step = step_data[0]
                    logger.info(f"  → First step: {first_step.get('step_no')} - {first_step.get('action')}")

                # Update in-memory state as working copy (but JSON remains authoritative)
                self.step_table_json = step_data.copy()
                self.total_steps = len(step_data)

                logger.info("=== get_effective_step_table() completed (JSON-ONLY) ===")
                return step_data
            else:
                error_msg = f"No step data found in JSON storage for test case: {test_case_id}"
                logger.error(error_msg)
                logger.error("This test case needs to be converted first in Stage 3")
                raise ValueError(error_msg)

        except Exception as e:
            error_msg = f"Failed to load step data from JSON storage: {e}"
            logger.error(error_msg)
            raise ValueError(error_msg)

    def validate_step_data_consistency(self, stage_name: str = "Unknown") -> Dict[str, Any]:
        """
        Validate that step data is consistent across different sources for hybrid editing debugging.

        Args:
            stage_name: Name of the stage calling this validation (for logging)

        Returns:
            Dict containing validation results and recommendations
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info(f"=== Step Data Consistency Validation ({stage_name}) ===")

        validation_results = {
            "stage": stage_name,
            "hybrid_editing_active": False,
            "data_sources_consistent": True,
            "issues": [],
            "recommendations": [],
            "step_data_summary": {}
        }

        # Check if hybrid editing is active
        validation_results["hybrid_editing_active"] = (
            self.hybrid_editing_enabled and
            bool(self.step_insertion_points or self.combined_step_table)
        )

        # Get current step data
        selected_step = getattr(self, 'selected_step', None)
        selected_step_table_entry = getattr(self, 'selected_step_table_entry', None)

        if selected_step and selected_step_table_entry:
            step_no = selected_step.get('Step No')
            original_action = selected_step.get('Test Steps', '')
            step_table_action = selected_step_table_entry.get('action', '')

            validation_results["step_data_summary"] = {
                "step_no": step_no,
                "original_action": original_action,
                "step_table_action": step_table_action,
                "actions_match": original_action.lower() == step_table_action.lower(),
                "step_is_synthetic": selected_step.get('_is_synthetic', False),
                "step_is_manual": selected_step_table_entry.get('_is_manual', False),
                "step_is_ai_generated": selected_step_table_entry.get('_is_ai_generated', False)
            }

            # Check for data inconsistencies
            if original_action.lower() != step_table_action.lower():
                validation_results["data_sources_consistent"] = False
                validation_results["issues"].append(
                    f"Action mismatch: Original '{original_action}' vs Step Table '{step_table_action}'"
                )

                if validation_results["hybrid_editing_active"]:
                    validation_results["recommendations"].append(
                        "Hybrid editing detected - ensure step_table_entry is used as authoritative source"
                    )
                else:
                    validation_results["recommendations"].append(
                        "Action mismatch without hybrid editing - investigate data synchronization"
                    )

        # Check effective step table consistency
        effective_steps = self.get_effective_step_table()
        original_steps = self.step_table_json or []

        if len(effective_steps) != len(original_steps):
            validation_results["issues"].append(
                f"Step count mismatch: Effective {len(effective_steps)} vs Original {len(original_steps)}"
            )
            validation_results["recommendations"].append(
                "Step count difference indicates hybrid editing - verify all stages use get_effective_step_table()"
            )

        # Log validation results
        logger.info(f"Validation Results for {stage_name}:")
        logger.info(f"  - Hybrid editing active: {validation_results['hybrid_editing_active']}")
        logger.info(f"  - Data sources consistent: {validation_results['data_sources_consistent']}")
        logger.info(f"  - Issues found: {len(validation_results['issues'])}")

        for issue in validation_results["issues"]:
            logger.warning(f"  - Issue: {issue}")

        for rec in validation_results["recommendations"]:
            logger.info(f"  - Recommendation: {rec}")

        logger.info(f"=== Validation completed for {stage_name} ===")

        return validation_results

    def get_effective_total_steps(self) -> int:
        """
        Get the effective total step count, considering hybrid editing.

        Returns:
            Total number of steps to process
        """
        effective_steps = self.get_effective_step_table()
        return len(effective_steps) if effective_steps else 0

    def sync_step_table_with_combined(self):
        """
        Synchronize the main step table with combined steps if hybrid editing is active.
        This ensures all stages use the combined steps.
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.hybrid_editing_enabled and self.step_insertion_points:
            # Get the latest combined steps
            combined_steps = self.get_combined_steps()
            if combined_steps:
                # Log the synchronization for debugging
                old_step_count = len(self.step_table_json) if self.step_table_json else 0
                new_step_count = len(combined_steps)
                logger.info(f"StateManager: Syncing step table - {old_step_count} → {new_step_count} steps")

                # Update main step table
                self.step_table_json = combined_steps
                self.combined_step_table = combined_steps

                # Update total steps
                old_total = self.total_steps
                self.total_steps = len(combined_steps)

                if old_total != self.total_steps:
                    logger.info(f"StateManager: Updated total_steps: {old_total} → {self.total_steps}")

                # Generate updated markdown
                from core.step_merger import StepMerger
                merger = StepMerger()
                merger.set_ai_steps(self.ai_generated_steps or [])
                for point, steps in self.step_insertion_points.items():
                    for step in steps:
                        merger.add_manual_step(step, point)

                self.step_table_markdown = merger.generate_markdown_table(combined_steps)

                logger.info(f"Synchronized step table with combined steps: {old_total} -> {self.total_steps} steps")

                # Save to persistent JSON storage
                self.save_step_data_to_json(combined_steps, {
                    'source': 'hybrid_editing_sync',
                    'ai_steps_count': len(self.ai_generated_steps or []),
                    'manual_steps_count': len(combined_steps) - len(self.ai_generated_steps or []),
                    'sync_timestamp': datetime.now().isoformat()
                })

    def save_step_data_to_json(self, step_data: List[Dict[str, Any]], metadata: Dict[str, Any] = None) -> bool:
        """
        Save step data to persistent JSON storage.

        Args:
            step_data: List of step dictionaries to save
            metadata: Additional metadata about the step data

        Returns:
            bool: True if saved successfully, False otherwise
        """
        import logging
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            logger.warning("Cannot save step data: no test case selected")
            return False

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            logger.warning("Cannot save step data: test case has no ID")
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            # Prepare metadata
            save_metadata = metadata or {}
            save_metadata.update({
                'test_case_objective': self.selected_test_case.get('Test Case Objective', ''),
                'hybrid_editing_enabled': self.hybrid_editing_enabled,
                'has_manual_steps': bool(self.step_insertion_points),
                'save_timestamp': datetime.now().isoformat()
            })

            success = storage.save_step_data(test_case_id, step_data, save_metadata)

            if success:
                logger.info(f"Successfully saved step data to JSON for test case: {test_case_id}")
                logger.info(f"  → Step count: {len(step_data)}")
                logger.info(f"  → Metadata: {save_metadata}")
            else:
                logger.error(f"Failed to save step data to JSON for test case: {test_case_id}")

            return success

        except Exception as e:
            logger.error(f"Error saving step data to JSON: {e}")
            return False

    def load_step_data_from_json(self) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """
        Load step data from persistent JSON storage.

        Returns:
            Tuple of (step_data, metadata) or None if not found
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not hasattr(self, 'selected_test_case') or not self.selected_test_case:
            logger.warning("Cannot load step data: no test case selected")
            return None

        test_case_id = self.selected_test_case.get('Test Case ID')
        if not test_case_id:
            logger.warning("Cannot load step data: test case has no ID")
            return None

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            result = storage.load_step_data(test_case_id)

            if result:
                step_data, metadata = result
                logger.info(f"Successfully loaded step data from JSON for test case: {test_case_id}")
                logger.info(f"  → Step count: {len(step_data)}")
                logger.info(f"  → Metadata: {metadata}")
            else:
                logger.info(f"No step data found in JSON for test case: {test_case_id}")

            return result

        except Exception as e:
            logger.error(f"Error loading step data from JSON: {e}")
            return None

    def update_step_data_in_json(self, step_data: List[Dict[str, Any]], operation: str, additional_metadata: Dict[str, Any] = None) -> bool:
        """
        Immediately update step data in JSON storage after any modification.
        This ensures JSON files remain the single source of truth.

        Args:
            step_data: Updated step data to save
            operation: Description of the operation that triggered the update
            additional_metadata: Additional metadata to include

        Returns:
            bool: True if update was successful, False otherwise
        """
        import logging
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info(f"🔄 Updating step data in JSON storage - Operation: {operation}")

        # Prepare metadata
        metadata = {
            'source': 'real_time_update',
            'operation': operation,
            'update_timestamp': datetime.now().isoformat(),
            'step_count': len(step_data)
        }

        if additional_metadata:
            metadata.update(additional_metadata)

        # Save to JSON storage
        success = self.save_step_data_to_json(step_data, metadata)

        if success:
            logger.info(f"✅ Successfully updated JSON storage for operation: {operation}")
            # Update in-memory working copy
            self.step_table_json = step_data.copy()
            self.total_steps = len(step_data)
        else:
            logger.error(f"❌ Failed to update JSON storage for operation: {operation}")

        return success

    def get_current_step_from_json(self, step_index: int) -> Optional[Dict[str, Any]]:
        """
        Get a specific step from JSON storage by index.

        Args:
            step_index: Index of the step to retrieve

        Returns:
            Step dictionary or None if not found
        """
        try:
            step_data = self.get_effective_step_table()
            if 0 <= step_index < len(step_data):
                return step_data[step_index]
            else:
                return None
        except ValueError as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to get step {step_index} from JSON: {e}")
            return None

    def advance_to(self, new_stage: StateStage, reason: str = "") -> bool:
        """
        Centralized stage transition method with validation and comprehensive flag cleanup.

        This method serves as the single source of truth for stage transitions,
        replacing the fragile distributed boolean flag manipulation that causes phantom stage jumps.

        Args:
            new_stage: The target stage to transition to
            reason: The reason for the transition (for logging)

        Returns:
            bool: True if transition was successful, False if invalid
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Validate transition is legal
        current_stage_num = self.current_stage.get_stage_number()
        target_stage_num = new_stage.get_stage_number()

        # Stage 9 (Script Browser) is always accessible from any stage
        if target_stage_num == 9:
            logger.info(f"Allowing transition to Stage 9 (Script Browser) from Stage {current_stage_num}")
            # Update the authoritative current stage
            self.current_stage = new_stage
            logger.info(f"State change: current_stage = {self.current_stage.get_display_name()}")
            return True

        # Define legal transitions (can go forward, or specific backward transitions)
        legal_backward_transitions = {
            # Stage 7 -> Stage 4 (after script execution, return to step selection)
            (7, 4): "Script execution completed, returning to step selection",
            # Stage 8 -> Stage 3 (after optimization, return to test case selection)
            (8, 3): "Script optimization completed, returning to test case selection",
            # Stage 9 -> Any stage (from script browser to any workflow stage)
            (9, 1): "Script browser navigation to Stage 1",
            (9, 2): "Script browser navigation to Stage 2",
            (9, 3): "Script browser navigation to Stage 3",
            (9, 4): "Script browser navigation to Stage 4",
            (9, 5): "Script browser navigation to Stage 5",
            (9, 6): "Script browser navigation to Stage 6",
            (9, 7): "Script browser navigation to Stage 7",
            (9, 8): "Script browser navigation to Stage 8",
            # Stage 10 -> Any stage (from script playground to any workflow stage)
            (10, 1): "Script playground navigation to Stage 1",
            (10, 2): "Script playground navigation to Stage 2",
            (10, 3): "Script playground navigation to Stage 3",
            (10, 4): "Script playground navigation to Stage 4",
            (10, 5): "Script playground navigation to Stage 5",
            (10, 6): "Script playground navigation to Stage 6",
            (10, 7): "Script playground navigation to Stage 7",
            (10, 8): "Script playground navigation to Stage 8",
            (10, 9): "Script playground navigation to Stage 9",
            # Stage 4 -> Stage 4 (step navigation within Stage 4)
            (4, 4): "Step navigation within Stage 4",
            # Any stage -> Stage 3 (test case reset)
            (4, 3): "Test case reset from Stage 4",
            (5, 3): "Test case reset from Stage 5",
            (6, 3): "Test case reset from Stage 6",
            (7, 3): "Test case reset from Stage 7",
            (8, 3): "Test case reset from Stage 8",
            # Any stage -> Stage 9 (script browser access)
            (1, 9): "Access script browser from Stage 1",
            (2, 9): "Access script browser from Stage 2",
            (3, 9): "Access script browser from Stage 3",
            (4, 9): "Access script browser from Stage 4",
            (5, 9): "Access script browser from Stage 5",
            (6, 9): "Access script browser from Stage 6",
            (7, 9): "Access script browser from Stage 7",
            (8, 9): "Access script browser from Stage 8",
            # Any stage -> Stage 10 (script playground access)
            (1, 10): "Access script playground from Stage 1",
            (2, 10): "Access script playground from Stage 2",
            (3, 10): "Access script playground from Stage 3",
            (4, 10): "Access script playground from Stage 4",
            (5, 10): "Access script playground from Stage 5",
            (6, 10): "Access script playground from Stage 6",
            (7, 10): "Access script playground from Stage 7",
            (8, 10): "Access script playground from Stage 8",
            (9, 10): "Access script playground from Stage 9"
        }

        # Check if transition is legal
        is_forward = target_stage_num > current_stage_num
        is_same_stage = target_stage_num == current_stage_num
        is_legal_backward = (current_stage_num, target_stage_num) in legal_backward_transitions

        # CRITICAL FIX: Enhanced complete reset logic with safety checks
        is_complete_reset = False
        if target_stage_num == 1:
            # CRITICAL FIX: Allow Stage 1 transitions based on progress analysis
            has_file = bool(getattr(self, 'uploaded_excel', None) or getattr(self, 'uploaded_file', None))
            has_test_cases = bool(getattr(self, 'test_cases', None))
            has_website = bool(getattr(self, 'website_url', None) and self.website_url != "https://example.com")
            has_selected_test_case = bool(getattr(self, 'selected_test_case', None))
            has_conversion = bool(getattr(self, 'conversion_done', None))

            # Allow Stage 1 if there's no significant progress
            if not (has_file or has_test_cases or has_website or has_selected_test_case or has_conversion):
                is_complete_reset = True
                logger.info("Stage 1 transition allowed: no significant progress detected")
            # For stages 4+, require explicit confirmation due to high risk of data loss
            elif current_stage_num >= 4:
                logger.warning("CRITICAL FIX: Blocking Stage 1 transition from advanced stage - user has significant progress")
                logger.warning(f"  → File: {has_file}, Test Cases: {has_test_cases}")
                logger.warning(f"  → Website: {has_website}, Selected Test Case: {has_selected_test_case}")
                logger.warning(f"  → Conversion: {has_conversion}")
                logger.warning("Use force_reset_to_stage1() method if you want to clear progress")
                return False
            # For stages 2-3, allow with warning (less data loss risk)
            else:
                is_complete_reset = True
                logger.warning("Stage 1 transition allowed from early stage with progress")
                logger.warning(f"  → File: {has_file}, Test Cases: {has_test_cases}")
                logger.warning(f"  → Website: {has_website}, Selected Test Case: {has_selected_test_case}")
                logger.warning("This will clear user progress!")

        if not (is_forward or is_same_stage or is_legal_backward or is_complete_reset):
            logger.error(f"Illegal stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.error(f"Legal transitions from Stage {current_stage_num}: forward to {current_stage_num + 1}-10, or specific backward transitions")
            return False

        # Log the transition
        if is_legal_backward:
            transition_reason = legal_backward_transitions.get((current_stage_num, target_stage_num), reason)
            logger.info(f"Legal backward stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.info(f"Transition reason: {transition_reason}")
        elif is_complete_reset:
            logger.info(f"Complete application reset: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.info(f"Reset reason: {reason}")
        else:
            logger.info(f"Stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.info(f"Transition reason: {reason}")

        # Store previous stage for cleanup logic
        previous_stage = self.current_stage

        # CRITICAL FIX: Record stage transition for monitoring
        try:
            from utils.stage_monitor import record_stage_transition
            record_stage_transition(previous_stage, new_stage, reason, self)
        except Exception as e:
            logger.warning(f"Failed to record stage transition: {e}")

        # Update the authoritative current stage
        self.current_stage = new_stage
        logger.info(f"State change: current_stage = {self.current_stage.get_display_name()}")

        # Perform stage-specific cleanup based on transition type
        self._cleanup_flags_for_stage_transition(previous_stage, new_stage, reason)

        return True

    def force_reset_to_stage1(self, reason: str = "Explicit user reset") -> bool:
        """
        Force a reset to Stage 1, bypassing all safety checks.

        This method should only be used when the user explicitly wants to reset
        the application and lose all progress.

        Args:
            reason: The reason for the forced reset

        Returns:
            bool: True if reset was successful
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.warning(f"FORCE RESET: Resetting to Stage 1 - {reason}")
        logger.warning("This will clear ALL user progress!")

        # Store previous stage for cleanup
        previous_stage = self.current_stage

        # Record the transition
        try:
            from utils.stage_monitor import record_stage_transition
            record_stage_transition(previous_stage, StateStage.STAGE1_UPLOAD, f"FORCE RESET: {reason}", self)
        except Exception as e:
            logger.warning(f"Failed to record stage transition: {e}")

        # Force update to Stage 1
        self.current_stage = StateStage.STAGE1_UPLOAD
        logger.info(f"State change: current_stage = {self.current_stage.get_display_name()} (FORCE RESET)")

        # Clear ALL progress
        self.uploaded_excel = None
        self.uploaded_file = None
        self.test_cases = None
        self.website_url = "https://example.com"
        self.selected_test_case = None
        self.conversion_done = False
        self.step_table_json = []
        self.selected_step = None
        self.step_matches = {}
        self.element_matches = {}
        self.test_data = {}
        self.test_data_skipped = False
        self.generated_script_path = None
        self.all_steps_done = False

        # Perform cleanup
        self._cleanup_flags_for_stage_transition(previous_stage, StateStage.STAGE1_UPLOAD, reason)

        logger.warning("FORCE RESET COMPLETE: All progress cleared")
        return True

    def _cleanup_flags_for_stage_transition(self, previous_stage: StateStage, target_stage: StateStage, reason: str):
        """
        Clean up boolean flags that could interfere with stage detection.

        This method ensures that stale flags from previous stages don't cause
        phantom stage jumps by systematically clearing flags that are no longer relevant.

        Args:
            previous_stage: The stage we're transitioning from
            target_stage: The stage we're transitioning to
            reason: The reason for the transition (for logging)
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info(f"Cleaning up flags for transition: {previous_stage.get_display_name()} -> {target_stage.get_display_name()}")

        target_stage_num = target_stage.get_stage_number()

        # Clear flags that are ahead of the target stage to prevent phantom jumps
        if target_stage_num < 3:
            # Going back to Stage 1 or 2 - clear all test case related flags
            if self.conversion_done:
                self.conversion_done = False
                logger.info("State change: conversion_done = False (stage cleanup)")

        if target_stage_num < 4:
            # Going back before Stage 4 - clear step selection flags
            if self.step_ready_for_script:
                self.step_ready_for_script = False
                logger.info("State change: step_ready_for_script = False (stage cleanup)")

        if target_stage_num < 5:
            # Going back before Stage 5 - clear element matching flags
            if self.step_matches:
                self.step_matches = {}
                logger.info("State change: step_matches = {} (stage cleanup)")
            if self.element_matches:
                self.element_matches = {}
                logger.info("State change: element_matches = {} (stage cleanup)")

        if target_stage_num < 6:
            # Going back before Stage 6 - clear test data flags
            if self.test_data:
                self.test_data = {}
                logger.info("State change: test_data = {} (stage cleanup)")
            if self.test_data_skipped:
                self.test_data_skipped = False
                logger.info("State change: test_data_skipped = False (stage cleanup)")

        if target_stage_num < 7:
            # Going back before Stage 7 - clear script generation flags
            if self.generated_script_path:
                self.generated_script_path = None
                logger.info("State change: generated_script_path = None (stage cleanup)")
            if self.script_just_generated:
                self.script_just_generated = False
                logger.info("State change: script_just_generated = False (stage cleanup)")
            if self.script_validation_done:
                self.script_validation_done = False
                logger.info("State change: script_validation_done = False (stage cleanup)")
            if self.script_validation_passed:
                self.script_validation_passed = False
                logger.info("State change: script_validation_passed = False (stage cleanup)")

        if target_stage_num < 8:
            # Going back before Stage 8 - clear optimization flags
            if self.all_steps_done:
                self.all_steps_done = False
                logger.info("State change: all_steps_done = False (stage cleanup)")
            if self.optimization_in_progress:
                self.optimization_in_progress = False
                logger.info("State change: optimization_in_progress = False (stage cleanup)")
            if self.optimization_complete:
                self.optimization_complete = False
                logger.info("State change: optimization_complete = False (stage cleanup)")

        # Special case: Stage 7 -> Stage 4 transition (step advancement)
        if previous_stage == StateStage.STAGE7_EXECUTE and target_stage == StateStage.STAGE4_DETECT:
            # Don't clear test case level flags, only step-specific ones
            logger.info("Stage 7 -> Stage 4 transition: preserving test case state, clearing step state")
            # The reset_step_state method will be called separately

        # Special case: Stage 8 -> Stage 3 transition (new test case)
        elif previous_stage == StateStage.STAGE8_OPTIMIZE and target_stage == StateStage.STAGE3_CONVERT:
            # Clear all test case state for fresh start
            logger.info("Stage 8 -> Stage 3 transition: clearing all test case state")
            # The reset_test_case_state method will be called separately

        # Special case: Stage 9 -> Stage 8 transition (back to optimization)
        elif previous_stage == StateStage.STAGE9_BROWSE and target_stage == StateStage.STAGE8_OPTIMIZE:
            # Preserve all optimization state when returning from script browser
            logger.info("Stage 9 -> Stage 8 transition: preserving optimization state")
            # No cleanup needed - user is returning to continue optimization work

        # Special case: Stage 10 transitions (script playground)
        elif previous_stage == StateStage.STAGE10_PLAYGROUND:
            # Stage 10 is independent - preserve all workflow state when leaving playground
            logger.info(f"Stage 10 -> {target_stage.get_display_name()} transition: preserving workflow state")
            # No cleanup needed - Stage 10 doesn't modify workflow state

        elif target_stage == StateStage.STAGE10_PLAYGROUND:
            # Entering Stage 10 - preserve all workflow state
            logger.info(f"{previous_stage.get_display_name()} -> Stage 10 transition: preserving workflow state")
            # No cleanup needed - Stage 10 is independent and doesn't interfere with workflow

        logger.info(f"Flag cleanup completed for {previous_stage.get_display_name()} -> {target_stage.get_display_name()}")




    def update_script_continuity(self, script: str, step_no: str):
        """
        Store the generated (or merged) script and extract key elements for continuity.

        Args:
            script: The script content to analyze and store
            step_no: The step number this script belongs to
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Store the complete script
        self.previous_scripts[step_no] = script
        logger.info(f"Stored script for step {step_no} (length: {len(script)} chars)")

        # Extract imports, fixtures, functions and variables
        script_lines = script.splitlines()
        for line in script_lines:
            line_stripped = line.strip()

            # Track imports
            if line_stripped.startswith(('import ', 'from ')):
                if line_stripped not in self.script_imports:
                    self.script_imports.append(line_stripped)
                    logger.info(f"Added import: {line_stripped}")

            # Track fixtures
            elif line_stripped.startswith('@pytest.fixture'):
                # Extract fixture name from the next line (assumes fixture definition follows decorator)
                next_line_idx = script.splitlines().index(line) + 1
                if next_line_idx < len(script.splitlines()):
                    next_line = script.splitlines()[next_line_idx]
                    if 'def ' in next_line:
                        fn = next_line.split('def')[-1].split('(')[0].strip()
                        if fn not in [f.split()[-1] for f in self.script_fixtures]:
                            self.script_fixtures.append(line_stripped)
                            logger.info(f"Added fixture: {fn}")

            # Track helper functions (non-test functions)
            elif line_stripped.startswith('def ') and not line_stripped.startswith('def test_'):
                fn = line_stripped.split()[1].split('(')[0]
                if fn not in self.script_functions:
                    self.script_functions[fn] = line_stripped
                    logger.info(f"Added helper function: {fn}")

            # Track variable definitions
            elif '=' in line_stripped and not line_stripped.startswith(('#', '/', 'if', 'for', 'while', 'def')):
                var_parts = line_stripped.split('=', 1)
                var_name = var_parts[0].strip()
                var_value = var_parts[1].strip()
                if var_name and not var_name.startswith(('_', 'test_')) and var_name not in ['driver']:
                    self.script_variables[var_name] = var_value
                    logger.info(f"Added variable: {var_name} = {var_value}")

        # Check if browser is initialized
        if not self.browser_initialized and any(browser_init in script for browser_init in
                                             ['driver = webdriver.Chrome(', 'driver = webdriver.Firefox(', 'driver = webdriver.Edge(']):
            self.browser_initialized = True
            logger.info("Browser initialization detected")

        return True

    def add_script_to_history(self, script_content: str, script_type: str, step_no: str = None, file_path: str = None, metadata: Dict[str, Any] = None):
        """
        Add a script to the script history for Stage 9 browsing.

        Args:
            script_content: The script content
            script_type: Type of script ('step', 'combined', 'optimized')
            step_no: Step number (for step scripts)
            file_path: Path to the script file
            metadata: Additional metadata about the script
        """
        import logging
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Create script entry
        script_entry = {
            'id': f"{script_type}_{step_no or 'all'}_{int(datetime.now().timestamp())}",
            'content': script_content,
            'type': script_type,
            'step_no': step_no,
            'file_path': file_path,
            'timestamp': datetime.now(),
            'test_case_id': self.selected_test_case.get('Test Case ID', 'unknown') if self.selected_test_case else 'unknown',
            'metadata': metadata or {}
        }

        # Add to history
        self.script_history.append(script_entry)

        # Store metadata separately for quick access
        self.script_metadata[script_entry['id']] = {
            'type': script_type,
            'step_no': step_no,
            'timestamp': script_entry['timestamp'],
            'test_case_id': script_entry['test_case_id'],
            'file_size': len(script_content),
            'line_count': len(script_content.splitlines()),
            'metadata': metadata or {}
        }

        logger.info(f"Added {script_type} script to history: {script_entry['id']}")

        # Also save to persistent storage
        self._save_to_persistent_storage(script_content, script_type, step_no, file_path, metadata)

    def save_manual_script_edit(self, edited_content: str, reason: str = "Manual edit"):
        """
        Save manually edited script content and update state.

        Args:
            edited_content: The manually edited script content
            reason: Reason for the edit (for logging)
        """
        import logging
        import os
        import time
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Store original AI content if not already stored
        if not self.script_manually_edited and self.last_script_content:
            self.original_ai_script_content = self.last_script_content
            logger.info("Stored original AI-generated script for revert capability")

        # Update script content
        self.last_script_content = edited_content
        self.script_manually_edited = True
        self.manual_edit_timestamp = datetime.now()
        self.script_edit_mode = False  # Exit edit mode after saving

        # Create new file for edited script
        if self.selected_test_case and self.selected_step_table_entry:
            test_case_id = self.selected_test_case.get('Test Case ID', 'unknown')
            step_no = self.selected_step_table_entry.get('Step', 'unknown')

            script_dir = "generated_tests"
            os.makedirs(script_dir, exist_ok=True)

            # Create filename with manual edit indicator
            edited_script_file = os.path.join(
                script_dir,
                f"test_{test_case_id}_step_{step_no}_manual_edit_{int(time.time())}.py"
            )

            # Save edited script to file
            with open(edited_script_file, "w") as f:
                f.write(edited_content)

            # Update file path in state
            self.last_script_file = edited_script_file
            self.generated_script_path = edited_script_file  # CRITICAL FIX: Update generated_script_path
            logger.info(f"Saved manually edited script to: {edited_script_file}")

            # CRITICAL FIX: Update JSON storage with new script file path
            try:
                current_step_data = self.get_effective_step_table()
                step_no_str = str(step_no)

                # Find and update the current step with new script file path
                for step in current_step_data:
                    if str(step.get('step_no')) == step_no_str:
                        # Update script file path to point to manually edited version
                        step['_script_file_path'] = edited_script_file
                        step['_script_manually_edited'] = True
                        step['_script_manual_edit_timestamp'] = self.manual_edit_timestamp.isoformat()
                        step['_script_edit_reason'] = reason
                        step['_script_content_length'] = len(edited_content)
                        break

                # Save updated step data to JSON (single source of truth)
                self.update_step_data_in_json(current_step_data, f"manual_script_edit_step_{step_no_str}", {
                    'step_no': step_no_str,
                    'edited_script_file_path': edited_script_file,
                    'edit_reason': reason,
                    'edit_timestamp': self.manual_edit_timestamp.isoformat(),
                    'script_content_length': len(edited_content),
                    'generation_method': 'manual_edit'
                })
                logger.info(f"✅ CRITICAL FIX: Updated JSON storage with manually edited script path for step {step_no_str}")
                logger.info(f"  → New script path in JSON: {edited_script_file}")
            except Exception as e:
                logger.error(f"❌ CRITICAL ERROR: Failed to update JSON storage with manually edited script path: {e}")

            # Add to script history with manual edit metadata
            metadata = {
                'manually_edited': True,
                'edit_timestamp': self.manual_edit_timestamp.isoformat(),
                'edit_reason': reason,
                'original_ai_script_available': bool(self.original_ai_script_content)
            }

            self.add_script_to_history(
                script_content=edited_content,
                script_type='step_manual_edit',
                step_no=step_no,
                file_path=edited_script_file,
                metadata=metadata
            )

            logger.info(f"Manual script edit saved: {reason}")

    def revert_to_ai_script(self):
        """
        Revert manually edited script back to original AI-generated version.
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not self.script_manually_edited or not self.original_ai_script_content:
            logger.warning("Cannot revert: No manual edits or original AI script not available")
            return False

        # Restore original AI content
        self.last_script_content = self.original_ai_script_content
        self.script_manually_edited = False
        self.manual_edit_timestamp = None
        self.script_edit_mode = False

        # CRITICAL FIX: Update JSON storage to revert to original AI script path
        if self.selected_test_case and self.selected_step_table_entry:
            try:
                current_step_data = self.get_effective_step_table()
                step_no = self.selected_step_table_entry.get('Step', 'unknown')
                step_no_str = str(step_no)

                # Find the original AI script file path from script history
                original_ai_script_path = None
                for script in self.script_history:
                    if (script.get('step_no') == step_no_str and
                        script.get('type') in ['step', 'combined'] and
                        not script.get('metadata', {}).get('manually_edited', False)):
                        original_ai_script_path = script.get('file_path')
                        break

                if original_ai_script_path:
                    # Find and update the current step to revert to original AI script
                    for step in current_step_data:
                        if str(step.get('step_no')) == step_no_str:
                            # Revert script file path to original AI-generated version
                            step['_script_file_path'] = original_ai_script_path
                            step['_script_manually_edited'] = False
                            step.pop('_script_manual_edit_timestamp', None)
                            step.pop('_script_edit_reason', None)
                            step['_script_content_length'] = len(self.original_ai_script_content)
                            break

                    # Update generated_script_path in state
                    self.generated_script_path = original_ai_script_path
                    self.last_script_file = original_ai_script_path

                    # Save updated step data to JSON
                    self.update_step_data_in_json(current_step_data, f"revert_to_ai_script_step_{step_no_str}", {
                        'step_no': step_no_str,
                        'reverted_to_ai_script_path': original_ai_script_path,
                        'generation_method': 'revert_to_ai'
                    })
                    logger.info(f"✅ CRITICAL FIX: Reverted JSON storage to original AI script path for step {step_no_str}")
                    logger.info(f"  → Reverted script path in JSON: {original_ai_script_path}")
                else:
                    logger.warning(f"Could not find original AI script path for step {step_no_str} in script history")
            except Exception as e:
                logger.error(f"❌ CRITICAL ERROR: Failed to update JSON storage when reverting to AI script: {e}")

        logger.info("Reverted script to original AI-generated version")
        return True

    def toggle_script_edit_mode(self):
        """
        Toggle script edit mode on/off.
        """
        self.script_edit_mode = not self.script_edit_mode

        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")
        logger.info(f"Script edit mode: {'ON' if self.script_edit_mode else 'OFF'}")

    def get_script_status_info(self):
        """
        Get information about current script status for UI display.

        Returns:
            dict: Script status information including edit status, timestamps, etc.
        """
        status = {
            'is_manually_edited': self.script_manually_edited,
            'has_original_ai_script': bool(self.original_ai_script_content),
            'edit_timestamp': self.manual_edit_timestamp,
            'in_edit_mode': self.script_edit_mode,
            'can_revert': self.script_manually_edited and bool(self.original_ai_script_content)
        }

        return status

    def update_step_url_tracking(self, step_no: str, url_data: Dict[str, Any]) -> bool:
        """
        Update URL tracking information for a specific step.

        Args:
            step_no: Step number to update
            url_data: Dictionary containing URL tracking information

        Returns:
            bool: True if updated successfully, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not self.selected_test_case:
            logger.warning("No test case selected for URL tracking update")
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()
            test_case_id = self.selected_test_case.get('Test Case ID', '')

            success = storage.update_step_url_tracking(test_case_id, step_no, url_data)
            if success:
                logger.info(f"StateManager: Updated URL tracking for step {step_no}")
            return success

        except Exception as e:
            logger.error(f"StateManager: Failed to update URL tracking for step {step_no}: {e}")
            return False

    def get_step_url_history(self, step_no: str = None) -> Dict[str, Any]:
        """
        Get URL history for a specific step or all steps.

        Args:
            step_no: Optional step number to get history for (if None, returns all steps)

        Returns:
            Dict containing URL history information
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not self.selected_test_case:
            logger.warning("No test case selected for URL history retrieval")
            return {}

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()
            test_case_id = self.selected_test_case.get('Test Case ID', '')

            url_history = storage.get_step_url_history(test_case_id, step_no)
            logger.info(f"StateManager: Retrieved URL history for test case {test_case_id}")
            return url_history

        except Exception as e:
            logger.error(f"StateManager: Failed to get URL history: {e}")
            return {}

    def get_current_step_url(self) -> Optional[str]:
        """
        Get the current URL for the currently selected step.

        Returns:
            Optional[str]: Current URL for the step, or None if not available
        """
        if not self.selected_step_table_entry:
            return None

        step_no = self.selected_step_table_entry.get('step_no', '')
        url_history = self.get_step_url_history(step_no)

        if step_no in url_history:
            return url_history[step_no].get('current_url')

        return None

    def sync_script_path_for_current_step(self) -> bool:
        """
        Synchronize the generated_script_path with the current step's script file from JSON data.

        This method ensures that state.generated_script_path always points to the correct
        script file for the currently selected step, reading from JSON storage as the
        single source of truth.

        Returns:
            bool: True if script path was successfully synchronized, False otherwise
        """
        import logging
        import os
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info("=== sync_script_path_for_current_step() called ===")

        # Ensure we have a current step selected
        if not hasattr(self, 'selected_step_table_entry') or not self.selected_step_table_entry:
            logger.warning("No current step selected - cannot sync script path")
            return False

        current_step = self.selected_step_table_entry
        step_no = current_step.get('step_no', 'Unknown')

        # Get the script file path from JSON data
        script_file_path = current_step.get('_script_file_path')

        if not script_file_path:
            logger.warning(f"No script file path found in JSON data for step {step_no}")
            return False

        # Verify the script file exists
        if not os.path.exists(script_file_path):
            logger.error(f"Script file does not exist: {script_file_path}")
            logger.error(f"Cannot sync script path for step {step_no}")
            return False

        # Update the generated_script_path if it's different
        old_generated_script_path = getattr(self, 'generated_script_path', '')
        if old_generated_script_path != script_file_path:
            self.generated_script_path = script_file_path
            logger.info(f"State change: generated_script_path synchronized for step {step_no}")
            logger.info(f"  → Old path: {old_generated_script_path}")
            logger.info(f"  → New path: {script_file_path}")

            # Also update last_script_file for consistency
            self.last_script_file = script_file_path
            logger.info(f"State change: last_script_file synchronized: {script_file_path}")

            # Load script content if available
            try:
                with open(script_file_path, 'r') as f:
                    script_content = f.read()
                self.last_script_content = script_content
                logger.info(f"State change: last_script_content loaded ({len(script_content)} chars)")
            except Exception as e:
                logger.warning(f"Could not load script content from {script_file_path}: {e}")

            return True
        else:
            logger.info(f"Script path already synchronized for step {step_no}: {script_file_path}")
            return True

    def _upgrade_existing_state(self, existing_state):
        """
        Upgrade existing state instance with new manual editing fields and methods.

        Args:
            existing_state: The existing StateManager instance to upgrade
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Add manual script editing fields if missing
        if not hasattr(existing_state, 'script_manually_edited'):
            existing_state.script_manually_edited = False
            logger.info("Added script_manually_edited field to existing state")

        if not hasattr(existing_state, 'original_ai_script_content'):
            existing_state.original_ai_script_content = ""
            logger.info("Added original_ai_script_content field to existing state")

        if not hasattr(existing_state, 'manual_edit_timestamp'):
            existing_state.manual_edit_timestamp = None
            logger.info("Added manual_edit_timestamp field to existing state")

        if not hasattr(existing_state, 'script_edit_mode'):
            existing_state.script_edit_mode = False
            logger.info("Added script_edit_mode field to existing state")

        # Add methods if missing (bind to existing instance)
        if not hasattr(existing_state, 'save_manual_script_edit'):
            existing_state.save_manual_script_edit = self.save_manual_script_edit.__get__(existing_state, type(existing_state))
            logger.info("Added save_manual_script_edit method to existing state")

        if not hasattr(existing_state, 'revert_to_ai_script'):
            existing_state.revert_to_ai_script = self.revert_to_ai_script.__get__(existing_state, type(existing_state))
            logger.info("Added revert_to_ai_script method to existing state")

        if not hasattr(existing_state, 'toggle_script_edit_mode'):
            existing_state.toggle_script_edit_mode = self.toggle_script_edit_mode.__get__(existing_state, type(existing_state))
            logger.info("Added toggle_script_edit_mode method to existing state")

        if not hasattr(existing_state, 'get_script_status_info'):
            existing_state.get_script_status_info = self.get_script_status_info.__get__(existing_state, type(existing_state))
            logger.info("Added get_script_status_info method to existing state")

        logger.info("State upgrade completed for manual script editing functionality")

    def _init_script_storage(self):
        """Initialize persistent script storage."""
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        try:
            from core.script_storage import get_script_storage, ScriptStorage

            # Get the storage instance
            self._script_storage = get_script_storage()

            # Verify the storage instance has required methods
            required_methods = ['clear_all_scripts', 'save_script', 'get_all_scripts', 'get_script_statistics']
            missing_methods = []

            for method in required_methods:
                if not hasattr(self._script_storage, method):
                    missing_methods.append(method)

            if missing_methods:
                logger.error(f"ScriptStorage instance missing required methods: {missing_methods}")
                logger.error(f"Available methods: {[method for method in dir(self._script_storage) if not method.startswith('_')]}")

                # Try to create a new instance directly
                logger.info("Attempting to create new ScriptStorage instance directly...")
                self._script_storage = ScriptStorage()

                # Re-check methods
                missing_methods = [method for method in required_methods if not hasattr(self._script_storage, method)]
                if missing_methods:
                    logger.error(f"New ScriptStorage instance still missing methods: {missing_methods}")
                    self._script_storage = None
                    return
                else:
                    logger.info("Successfully created new ScriptStorage instance with all required methods")

            # Load historical scripts into current session
            self._load_historical_scripts()

            logger.info("Initialized persistent script storage successfully")
            logger.info(f"ScriptStorage type: {type(self._script_storage)}")
            logger.info(f"Available methods: {[method for method in dir(self._script_storage) if not method.startswith('_')]}")

        except ImportError as e:
            logger.error(f"Failed to import script storage modules: {e}")
            self._script_storage = None
        except Exception as e:
            logger.error(f"Failed to initialize script storage: {e}", exc_info=True)
            self._script_storage = None

    def _save_to_persistent_storage(self, script_content: str, script_type: str, step_no: str = None,
                                   file_path: str = None, metadata: Dict[str, Any] = None):
        """Save script to persistent storage."""
        if self._script_storage is None:
            return

        try:
            test_case_id = None
            if hasattr(self, 'selected_test_case') and self.selected_test_case:
                test_case_id = self.selected_test_case.get('Test Case ID', 'unknown')

            script_id = self._script_storage.save_script(
                script_content=script_content,
                script_type=script_type,
                test_case_id=test_case_id,
                step_no=step_no,
                file_path=file_path,
                metadata=metadata
            )

            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.info(f"Saved script to persistent storage: {script_id}")

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to save script to persistent storage: {e}")

    def _load_historical_scripts(self):
        """Load historical scripts from persistent storage."""
        if self._script_storage is None:
            return

        try:
            # Get all scripts from storage
            historical_scripts = self._script_storage.get_all_scripts(include_current_session=True)

            # Convert to the format expected by script_history
            for script in historical_scripts:
                # Check if script is already in current session history
                existing_script = next(
                    (s for s in self.script_history if s.get('id') == script['id']),
                    None
                )

                if existing_script is None:
                    # Add to script history
                    self.script_history.append(script)

                    # Add to metadata
                    self.script_metadata[script['id']] = {
                        'type': script['type'],
                        'step_no': script['step_no'],
                        'timestamp': script['timestamp'],
                        'test_case_id': script['test_case_id'],
                        'file_size': script['file_size'],
                        'line_count': script['line_count'],
                        'metadata': script.get('metadata', {})
                    }

            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.info(f"Loaded {len(historical_scripts)} historical scripts")

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to load historical scripts: {e}")

    def get_all_scripts_with_history(self) -> List[Dict[str, Any]]:
        """
        Get all scripts including historical ones from persistent storage.

        Returns:
            List of all scripts with complete history
        """
        if self._script_storage is None:
            return self.script_history

        try:
            return self._script_storage.get_all_scripts(include_current_session=True)
        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to get scripts with history: {e}")
            return self.script_history

    def get_script_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about all scripts including historical ones.

        Returns:
            Dictionary containing script statistics
        """
        if self._script_storage is None:
            # Fallback to in-memory statistics
            from core.script_browser_helpers import get_script_statistics
            return get_script_statistics(self.script_history)

        try:
            return self._script_storage.get_script_statistics()
        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to get script statistics: {e}")
            # Fallback to in-memory statistics
            from core.script_browser_helpers import get_script_statistics
            return get_script_statistics(self.script_history)

    def clear_all_script_history(self, confirm: bool = False, reason: str = "") -> bool:
        """
        Clear ALL script history from both in-memory storage and persistent database.

        This method permanently deletes all scripts, metadata, and sessions.
        This action cannot be undone.

        Args:
            confirm: Whether the clear operation has been confirmed by the user
            reason: The reason for clearing (for logging)

        Returns:
            bool: True if history was cleared, False if not confirmed or failed
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not confirm:
            logger.warning(f"Attempted to clear all script history without confirmation: {reason}")
            return False

        logger.warning("=" * 80)
        logger.warning("CLEARING ALL SCRIPT HISTORY - PERMANENT ACTION")
        logger.warning("=" * 80)
        logger.warning(f"Clear reason: {reason}")

        try:
            deleted_count = 0
            persistent_storage_success = False

            # Clear persistent storage if available
            if self._script_storage is not None:
                try:
                    # Check if the clear_all_scripts method exists
                    if hasattr(self._script_storage, 'clear_all_scripts'):
                        deleted_count = self._script_storage.clear_all_scripts()
                        persistent_storage_success = True
                        logger.warning(f"Cleared {deleted_count} scripts from persistent storage")
                    else:
                        logger.error("ScriptStorage object missing 'clear_all_scripts' method")
                        logger.error(f"Available methods: {[method for method in dir(self._script_storage) if not method.startswith('_')]}")

                        # Try to reinitialize storage
                        logger.info("Attempting to reinitialize script storage...")
                        self._init_script_storage()

                        if self._script_storage and hasattr(self._script_storage, 'clear_all_scripts'):
                            deleted_count = self._script_storage.clear_all_scripts()
                            persistent_storage_success = True
                            logger.warning(f"Cleared {deleted_count} scripts from persistent storage (after reinit)")
                        else:
                            logger.error("Failed to reinitialize script storage with clear_all_scripts method")

                except AttributeError as e:
                    logger.error(f"AttributeError in persistent storage clear: {e}")
                    logger.error(f"ScriptStorage type: {type(self._script_storage)}")
                    logger.error(f"ScriptStorage methods: {[method for method in dir(self._script_storage) if not method.startswith('_')]}")
                    # Continue with in-memory clearing
                except Exception as e:
                    logger.error(f"Failed to clear persistent storage: {e}", exc_info=True)
                    # Continue with in-memory clearing even if persistent storage fails
            else:
                logger.warning("No persistent storage available - clearing in-memory only")

            # Clear in-memory script history
            in_memory_count = len(self.script_history)
            self.script_history.clear()
            self.script_metadata.clear()
            logger.warning(f"Cleared {in_memory_count} scripts from in-memory storage")

            # Reset script continuity tracking
            self.previous_scripts.clear()
            self.script_imports.clear()
            self.script_fixtures.clear()
            self.script_variables.clear()
            self.script_functions.clear()
            logger.warning("Cleared script continuity tracking data")

            total_cleared = max(deleted_count, in_memory_count)
            logger.warning(f"SCRIPT HISTORY CLEARED: Total {total_cleared} scripts removed")

            if not persistent_storage_success and self._script_storage is not None:
                logger.warning("WARNING: Persistent storage may not have been cleared completely")

            logger.warning("=" * 80)

            return True

        except Exception as e:
            logger.error(f"Error clearing script history: {e}", exc_info=True)
            return False

    def can_access_stage(self, target_stage: StateStage) -> bool:
        """
        Check if a stage can be accessed based on current state and prerequisites.

        Args:
            target_stage: The stage to check access for

        Returns:
            bool: True if stage is accessible, False otherwise
        """
        try:
            # Import navigation helpers to avoid circular imports
            from core.navigation_helpers import get_stage_accessibility

            accessibility_info = get_stage_accessibility(self, target_stage)
            return accessibility_info.get('accessible', False)

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Error checking stage accessibility: {e}")
            return False

    def get_stage_prerequisites(self, target_stage: StateStage) -> List[str]:
        """
        Get the list of missing prerequisites for a stage.

        Args:
            target_stage: The stage to check prerequisites for

        Returns:
            List of missing prerequisite descriptions
        """
        try:
            # Import navigation helpers to avoid circular imports
            from core.navigation_helpers import get_stage_accessibility

            accessibility_info = get_stage_accessibility(self, target_stage)
            return accessibility_info.get('missing_prerequisites', [])

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Error getting stage prerequisites: {e}")
            return ['Error checking prerequisites']









    def set_execution_error(self, error_details: Dict[str, Any]):
        """
        Set execution error state with detailed error information.

        Args:
            error_details: Dictionary containing error information including:
                - error_message: Main error message
                - traceback: Full traceback if available
                - returncode: Exit code from script execution
                - stdout: Standard output from execution
                - stderr: Standard error from execution
                - timestamp: When the error occurred
                - step_no: Which step failed
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        self.execution_error_occurred = True
        self.execution_error_acknowledged = False
        self.execution_error_details = error_details.copy()

        logger.info(f"State change: execution_error_occurred = True for step {error_details.get('step_no', 'Unknown')}")
        logger.error(f"Execution error details: {error_details.get('error_message', 'No message')}")

    def acknowledge_execution_error(self):
        """
        Mark the current execution error as acknowledged by the user.
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.execution_error_occurred and not self.execution_error_acknowledged:
            self.execution_error_acknowledged = True
            logger.info("State change: execution_error_acknowledged = True")
            return True
        return False

    def clear_execution_error(self):
        """
        Clear execution error state after user acknowledgment.
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.execution_error_occurred:
            logger.info("State change: clearing execution error state")
            self.execution_error_occurred = False
            self.execution_error_acknowledged = False
            self.execution_error_details = {}
            return True
        return False

    # ───── Browser Continuity Management ─────

    def set_browser_session_active(self, active: bool, url: Optional[str] = None):
        """
        Set browser session active state for continuity tracking.

        Args:
            active: Whether the browser session is active
            url: Current URL of the browser session
        """
        import logging
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.browser_session_active != active:
            self.browser_session_active = active
            logger.info(f"State change: browser_session_active = {active}")

            if active:
                self.browser_session_created_at = datetime.now()
                if url:
                    self.last_browser_session_url = url
                logger.info(f"Browser session activated at {self.browser_session_created_at}")
            else:
                self.session_ready_for_element_selection = False
                logger.info("Browser session deactivated")

    def set_session_ready_for_element_selection(self, ready: bool):
        """
        Set whether the browser session is ready for element selection.

        Args:
            ready: Whether the session is ready for element selection
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.session_ready_for_element_selection != ready:
            self.session_ready_for_element_selection = ready
            logger.info(f"State change: session_ready_for_element_selection = {ready}")

    def enable_browser_continuity(self):
        """Enable browser continuity for the workflow."""
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not self.browser_continuity_enabled:
            self.browser_continuity_enabled = True
            logger.info("State change: browser_continuity_enabled = True")

    def disable_browser_continuity(self):
        """Disable browser continuity and reset related state."""
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.browser_continuity_enabled:
            self.browser_continuity_enabled = False
            self.browser_session_active = False
            self.session_ready_for_element_selection = False
            self.last_browser_session_url = None
            self.browser_session_created_at = None
            logger.info("State change: browser continuity disabled and state reset")

    def get_browser_continuity_status(self) -> Dict[str, Any]:
        """
        Get current browser continuity status information.

        Returns:
            Dict containing browser continuity status
        """
        return {
            'enabled': self.browser_continuity_enabled,
            'session_active': self.browser_session_active,
            'ready_for_selection': self.session_ready_for_element_selection,
            'last_url': self.last_browser_session_url,
            'created_at': self.browser_session_created_at.isoformat() if self.browser_session_created_at else None
        }