"""
Integration Test for Browser State Continuity in Main Workflow

This script tests the integration of browser state continuity features
into the main GretahAI ScriptWeaver workflow.

Test Coverage:
1. StateManager browser continuity fields and methods
2. Stage 4 enhanced element selection integration
3. Stage 6 browser session initialization
4. Stage 7 script execution with session preservation
5. UI components integration
"""

import sys
import os
import logging
from unittest.mock import Mock, patch

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(__file__))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("test_integration")

def test_state_manager_browser_continuity():
    """Test StateManager browser continuity fields and methods."""
    print("🧪 Testing StateManager browser continuity integration...")
    
    try:
        from state_manager import StateManager, StateStage
        from datetime import datetime
        
        # Create a new StateManager instance
        state = StateManager()
        
        # Test initial state
        assert hasattr(state, 'browser_session_active'), "Missing browser_session_active field"
        assert hasattr(state, 'session_ready_for_element_selection'), "Missing session_ready_for_element_selection field"
        assert hasattr(state, 'browser_continuity_enabled'), "Missing browser_continuity_enabled field"
        assert hasattr(state, 'last_browser_session_url'), "Missing last_browser_session_url field"
        assert hasattr(state, 'browser_session_created_at'), "Missing browser_session_created_at field"
        
        # Test initial values
        assert state.browser_session_active == False
        assert state.session_ready_for_element_selection == False
        assert state.browser_continuity_enabled == False
        assert state.last_browser_session_url is None
        assert state.browser_session_created_at is None
        
        # Test browser continuity methods
        assert hasattr(state, 'set_browser_session_active'), "Missing set_browser_session_active method"
        assert hasattr(state, 'set_session_ready_for_element_selection'), "Missing set_session_ready_for_element_selection method"
        assert hasattr(state, 'enable_browser_continuity'), "Missing enable_browser_continuity method"
        assert hasattr(state, 'disable_browser_continuity'), "Missing disable_browser_continuity method"
        assert hasattr(state, 'get_browser_continuity_status'), "Missing get_browser_continuity_status method"
        
        # Test method functionality
        state.enable_browser_continuity()
        assert state.browser_continuity_enabled == True
        
        state.set_browser_session_active(True, "https://example.com")
        assert state.browser_session_active == True
        assert state.last_browser_session_url == "https://example.com"
        assert state.browser_session_created_at is not None
        
        state.set_session_ready_for_element_selection(True)
        assert state.session_ready_for_element_selection == True
        
        # Test status method
        status = state.get_browser_continuity_status()
        assert status['enabled'] == True
        assert status['session_active'] == True
        assert status['ready_for_selection'] == True
        assert status['last_url'] == "https://example.com"
        assert status['created_at'] is not None
        
        # Test disable functionality
        state.disable_browser_continuity()
        assert state.browser_continuity_enabled == False
        assert state.browser_session_active == False
        assert state.session_ready_for_element_selection == False
        assert state.last_browser_session_url is None
        assert state.browser_session_created_at is None
        
        print("✅ StateManager browser continuity integration: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ StateManager browser continuity integration: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_integration_imports():
    """Test that workflow integration components can be imported."""
    print("🧪 Testing workflow integration imports...")
    
    try:
        # Test core workflow integration
        from core.workflow_integration import (
            get_workflow_browser_integration,
            initialize_browser_continuity,
            is_browser_continuity_active,
            cleanup_browser_continuity,
            get_browser_continuity_info
        )
        
        # Test browser session manager
        from core.browser_session_manager import BrowserSessionManager
        
        # Test UI components
        from ui_components.browser_continuity_components import (
            render_browser_continuity_status,
            render_enhanced_element_selection_ui,
            render_script_execution_continuity_ui,
            render_continuity_workflow_indicator,
            show_continuity_help
        )
        
        print("✅ Workflow integration imports: PASSED")
        return True
        
    except ImportError as e:
        print(f"❌ Workflow integration imports: FAILED - {e}")
        return False
    except Exception as e:
        print(f"❌ Workflow integration imports: FAILED - {e}")
        return False

def test_stage_integration():
    """Test that stages have been properly integrated with browser continuity."""
    print("🧪 Testing stage integration...")
    
    try:
        # Test Stage 4 integration
        import stages.stage4 as stage4
        assert hasattr(stage4, '_handle_interactive_element_selection'), "Stage 4 missing enhanced element selection"
        assert hasattr(stage4, '_perform_standard_element_selection'), "Stage 4 missing standard element selection fallback"
        assert hasattr(stage4, '_process_selected_element'), "Stage 4 missing element processing function"
        
        # Test Stage 6 integration
        import stages.stage6 as stage6
        # Check that imports are present (they should be at module level)
        with open('stages/stage6.py', 'r', encoding='utf-8') as f:
            stage6_source = f.read()
        assert 'workflow_integration' in stage6_source, "Stage 6 missing workflow integration imports"
        assert 'browser_continuity_components' in stage6_source, "Stage 6 missing UI components imports"

        # Test Stage 7 integration
        import stages.stage7 as stage7
        with open('stages/stage7.py', 'r', encoding='utf-8') as f:
            stage7_source = f.read()
        assert 'workflow_integration' in stage7_source, "Stage 7 missing workflow integration imports"
        assert 'browser_continuity_components' in stage7_source, "Stage 7 missing UI components imports"
        
        print("✅ Stage integration: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Stage integration: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_components():
    """Test UI components functionality."""
    print("🧪 Testing UI components...")
    
    try:
        # Mock Streamlit for testing
        with patch('streamlit.sidebar'), \
             patch('streamlit.success'), \
             patch('streamlit.error'), \
             patch('streamlit.info'), \
             patch('streamlit.button'), \
             patch('streamlit.checkbox'), \
             patch('streamlit.expander'):
            
            from ui_components.browser_continuity_components import (
                render_browser_continuity_status,
                render_enhanced_element_selection_ui,
                render_script_execution_continuity_ui
            )
            
            # Mock state manager
            mock_state = Mock()
            mock_state.website_url = "https://example.com"
            
            # Test that functions can be called without errors
            # (They won't actually render due to mocking, but we can test they don't crash)
            try:
                render_browser_continuity_status()
                render_enhanced_element_selection_ui(mock_state)
                render_script_execution_continuity_ui(mock_state)
            except Exception as e:
                # Some exceptions are expected due to mocking, but not import errors
                if "import" in str(e).lower():
                    raise e
        
        print("✅ UI components: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ UI components: FAILED - {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_integration():
    """Test that app.py has been properly integrated."""
    print("🧪 Testing app.py integration...")
    
    try:
        # Check that app.py includes browser continuity in sidebar
        with open('app.py', 'r', encoding='utf-8') as f:
            app_source = f.read()
        assert 'render_browser_continuity_status' in app_source, "app.py missing browser continuity status in sidebar"
        
        print("✅ App integration: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ App integration: FAILED - {e}")
        return False

def run_all_tests():
    """Run all integration tests."""
    print("🚀 Starting Browser State Continuity Integration Tests")
    print("=" * 60)
    
    tests = [
        test_state_manager_browser_continuity,
        test_workflow_integration_imports,
        test_stage_integration,
        test_ui_components,
        test_app_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All integration tests PASSED! Browser continuity is ready for use.")
        return True
    else:
        print("⚠️ Some integration tests FAILED. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
