#!/usr/bin/env python3
"""
Enhanced Browser Session Continuity Test

This script tests the enhanced browser session continuity implementation
that allows script execution while preserving browser state for subsequent
element selection.

Key Features Tested:
- Browser session initialization and management
- Script execution with session preservation
- State continuity across Stage 7→4 transitions
- Test data injection into preserved sessions
- Session validation and error handling
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.browser_session_manager import BrowserSessionManager
from core.workflow_integration import WorkflowBrowserIntegration
from state_manager import StateManager
import streamlit as st

def create_test_script():
    """Create a simple test script for session continuity testing."""
    test_script_content = '''
import pytest
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

def test_session_continuity_example(browser, test_data):
    """Test script that uses preserved browser session."""
    print(f"Test data received: {test_data}")
    
    # Navigate to a test page if not already there
    current_url = browser.current_url
    print(f"Current URL: {current_url}")
    
    if "example.com" not in current_url:
        browser.get("https://example.com")
        time.sleep(2)
    
    # Verify page title
    title = browser.title
    print(f"Page title: {title}")
    assert "Example" in title
    
    # Find and interact with page elements
    try:
        # Look for the main heading
        heading = WebDriverWait(browser, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "h1"))
        )
        print(f"Found heading: {heading.text}")
        assert heading.is_displayed()
        
        # Scroll to demonstrate state change
        browser.execute_script("window.scrollTo(0, 100);")
        time.sleep(1)
        
        print("✅ Test completed successfully with preserved session")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise
'''
    
    # Create temporary script file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write(test_script_content)
        return f.name

def test_browser_session_manager():
    """Test the BrowserSessionManager functionality."""
    print("🧪 Testing BrowserSessionManager...")
    
    session_manager = BrowserSessionManager()
    
    # Test session creation
    print("📝 Creating browser session...")
    success = session_manager.create_session(headless=False)
    if not success:
        print("❌ Failed to create browser session")
        return False
    
    print("✅ Browser session created successfully")
    
    # Test navigation
    print("🌐 Testing navigation...")
    nav_success = session_manager.navigate_to("https://example.com")
    if not nav_success:
        print("❌ Failed to navigate to test page")
        session_manager.cleanup_session()
        return False
    
    print("✅ Navigation successful")
    
    # Test session info
    print("📊 Getting session info...")
    session_info = session_manager.get_session_info()
    print(f"  - Active: {session_info['active']}")
    print(f"  - URL: {session_info.get('current_url', 'N/A')}")
    print(f"  - Health Score: {session_info.get('health_score', 'N/A')}")
    
    # Test script execution with session preservation
    print("🧪 Testing script execution with session preservation...")
    script_path = create_test_script()
    
    try:
        test_data = {
            "website_url": "https://example.com",
            "test_case_id": "TEST_CONTINUITY_001",
            "step_no": 1
        }
        
        success, message, results = session_manager.execute_script_with_session(
            script_path, test_data
        )
        
        if success:
            print("✅ Script executed successfully with session preservation")
            print(f"   Message: {message}")
            print(f"   Results: {results.get('success', 'Unknown')}")
        else:
            print(f"❌ Script execution failed: {message}")
            return False
            
    finally:
        # Clean up temporary script
        try:
            os.unlink(script_path)
        except:
            pass
    
    # Test session state after execution
    print("🔍 Verifying session state after execution...")
    final_session_info = session_manager.get_session_info()
    if final_session_info['active']:
        print("✅ Session remains active after script execution")
        print(f"   Current URL: {final_session_info.get('current_url', 'N/A')}")
    else:
        print("❌ Session became inactive after script execution")
        return False
    
    # Clean up
    print("🧹 Cleaning up session...")
    session_manager.cleanup_session()
    print("✅ Session cleaned up successfully")
    
    return True

def test_workflow_integration():
    """Test the WorkflowBrowserIntegration functionality."""
    print("\n🧪 Testing WorkflowBrowserIntegration...")
    
    # Create a mock state manager
    class MockState:
        def __init__(self):
            self.website_url = "https://example.com"
            self.test_data = {"test_key": "test_value"}
            self.selected_step = {"Step No": 1}
    
    state = MockState()
    integration = WorkflowBrowserIntegration()
    
    # Test session initialization
    print("📝 Initializing workflow session...")
    success = integration.initialize_session(state, headless=False)
    if not success:
        print("❌ Failed to initialize workflow session")
        return False
    
    print("✅ Workflow session initialized successfully")
    
    # Test session info
    session_info = integration.get_session_info()
    print(f"📊 Session active: {session_info['active']}")
    
    # Test script execution with continuity
    print("🧪 Testing script execution with continuity...")
    script_path = create_test_script()
    
    try:
        success, message, results = integration.execute_script_with_continuity(
            state, script_path
        )
        
        if success:
            print("✅ Script executed successfully with workflow continuity")
            print(f"   Message: {message}")
            print(f"   Session preserved: {results.get('session_preserved', False)}")
            print(f"   Ready for element selection: {results.get('ready_for_element_selection', False)}")
        else:
            print(f"❌ Script execution with continuity failed: {message}")
            return False
            
    finally:
        # Clean up temporary script
        try:
            os.unlink(script_path)
        except:
            pass
    
    # Test transition preparation
    print("🔄 Testing transition to element selection...")
    transition_success = integration.transition_to_element_selection(state)
    if transition_success:
        print("✅ Transition to element selection prepared successfully")
    else:
        print("❌ Failed to prepare transition to element selection")
        return False
    
    # Clean up
    print("🧹 Cleaning up workflow integration...")
    integration.cleanup_session()
    print("✅ Workflow integration cleaned up successfully")
    
    return True

def main():
    """Main test function."""
    print("🚀 Enhanced Browser Session Continuity Test")
    print("=" * 50)
    
    # Test individual components
    session_manager_success = test_browser_session_manager()
    workflow_integration_success = test_workflow_integration()
    
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print(f"  BrowserSessionManager: {'✅ PASS' if session_manager_success else '❌ FAIL'}")
    print(f"  WorkflowBrowserIntegration: {'✅ PASS' if workflow_integration_success else '❌ FAIL'}")
    
    overall_success = session_manager_success and workflow_integration_success
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
    
    if overall_success:
        print("\n🎉 Enhanced browser session continuity is working correctly!")
        print("   The implementation supports:")
        print("   • Browser session preservation across script execution")
        print("   • Test data injection into preserved sessions")
        print("   • State continuity for Stage 7→4 transitions")
        print("   • Proper session management and cleanup")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
