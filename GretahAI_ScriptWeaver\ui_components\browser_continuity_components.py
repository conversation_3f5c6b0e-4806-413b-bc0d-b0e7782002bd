"""
UI Components for Browser State Continuity

This module provides Streamlit UI components for managing browser state
continuity across the GretahAI ScriptWeaver workflow stages.

Components:
- Browser session status indicator
- Session management controls
- Continuity workflow indicators
- Error handling and recovery UI
"""

import streamlit as st
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from core.workflow_integration import get_workflow_browser_integration, get_browser_continuity_info
from state_manager import StateManager

def render_browser_continuity_status() -> None:
    """
    Render enhanced browser continuity status in the sidebar.

    This component shows comprehensive browser session status, health metrics,
    performance indicators, and management controls.
    """
    integration = get_workflow_browser_integration()
    session_info = integration.get_session_info()

    st.sidebar.markdown("---")
    st.sidebar.markdown("### 🔄 Browser Continuity")

    if session_info.get('active'):
        # Active session with enhanced status
        health_score = session_info.get('health_score', 100)
        if health_score >= 90:
            st.sidebar.success("✅ Session Excellent")
        elif health_score >= 70:
            st.sidebar.warning("⚠️ Session Good")
        else:
            st.sidebar.error("🔴 Session Poor")

        # Quick metrics
        col1, col2 = st.sidebar.columns(2)
        with col1:
            time_remaining = session_info.get('time_remaining', 0)
            if time_remaining > 0:
                minutes = int(time_remaining // 60)
                if minutes > 60:
                    hours = minutes // 60
                    minutes = minutes % 60
                    time_display = f"{hours}h {minutes}m"
                else:
                    time_display = f"{minutes}m"
                st.metric("Time", time_display, delta=None, label_visibility="collapsed")
            else:
                st.metric("Time", "Expired", delta=None, label_visibility="collapsed")

        with col2:
            memory_usage = session_info.get('memory_usage_mb', 0)
            if memory_usage > 0:
                if memory_usage < 500:
                    st.metric("Memory", f"{memory_usage:.0f}MB", delta="🟢", label_visibility="collapsed")
                elif memory_usage < 1000:
                    st.metric("Memory", f"{memory_usage:.0f}MB", delta="🟡", label_visibility="collapsed")
                else:
                    st.metric("Memory", f"{memory_usage:.0f}MB", delta="🔴", label_visibility="collapsed")
            else:
                st.metric("Memory", "Unknown", delta=None, label_visibility="collapsed")

        # Session details
        with st.sidebar.expander("📊 Session Details", expanded=False):
            if session_info.get('current_url'):
                url_display = session_info['current_url']
                if len(url_display) > 40:
                    url_display = url_display[:37] + "..."
                st.write(f"**URL:** {url_display}")

            if session_info.get('title'):
                title_display = session_info['title']
                if len(title_display) > 30:
                    title_display = title_display[:27] + "..."
                st.write(f"**Page:** {title_display}")

            if session_info.get('session_id'):
                st.write(f"**Session:** {session_info['session_id'][:8]}...")

            session_age = session_info.get('session_age_minutes', 0)
            if session_age > 0:
                st.write(f"**Age:** {session_age:.0f} minutes")

            # Performance indicators
            cpu_usage = session_info.get('cpu_usage_percent', 0)
            if cpu_usage > 0:
                st.write(f"**CPU:** {cpu_usage:.1f}%")

        # Enhanced session controls
        st.sidebar.markdown("**Controls:**")

        col1, col2 = st.sidebar.columns(2)

        with col1:
            if st.button("🔄 Extend", key="extend_session", help="Extend session by 30 minutes"):
                if integration.extend_session(1800):  # 30 minutes
                    st.sidebar.success("✅ Extended!")
                    st.rerun()
                else:
                    st.sidebar.error("❌ Failed")

        with col2:
            if st.button("🛑 End", key="end_session", help="End browser session"):
                integration.cleanup_session()
                st.sidebar.success("✅ Ended")
                st.rerun()

        # Additional controls
        if st.sidebar.button("🔍 Validate", key="validate_session", help="Check session health"):
            st.sidebar.info("Validation available in main interface")

        if st.sidebar.button("📊 Metrics", key="show_metrics", help="Show detailed performance metrics"):
            st.sidebar.info("Detailed metrics available in main interface")

    else:
        # Inactive session with enhanced information
        st.sidebar.error("❌ Session Inactive")

        error_msg = session_info.get('error', 'Not initialized')
        if len(error_msg) > 30:
            error_msg = error_msg[:27] + "..."
        st.sidebar.caption(f"Status: {error_msg}")

        # Quick start option
        if st.sidebar.button("🚀 Quick Start", key="quick_start_session", help="Initialize browser session"):
            st.sidebar.info("💡 Use element selection to auto-start")

        # Help link
        if st.sidebar.button("❓ Help", key="continuity_help", help="Learn about browser continuity"):
            st.sidebar.info("💡 Help available in main interface")

def render_continuity_workflow_indicator(current_stage: str, 
                                       session_active: bool = False,
                                       script_executed: bool = False,
                                       ready_for_selection: bool = False) -> None:
    """
    Render workflow continuity indicators.
    
    Args:
        current_stage: Current workflow stage
        session_active: Whether browser session is active
        script_executed: Whether script has been executed
        ready_for_selection: Whether ready for element selection
    """
    st.markdown("#### 🔄 Workflow Continuity Status")
    
    # Create status indicators
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        session_icon = "🟢" if session_active else "⚪"
        st.markdown(f"{session_icon} **Browser Session**")
        if session_active:
            st.markdown("✅ Active")
        else:
            st.markdown("❌ Inactive")
    
    with col2:
        stage6_icon = "🟢" if current_stage in ['stage6', 'stage7', 'stage4'] else "⚪"
        st.markdown(f"{stage6_icon} **Stage 6: Generate**")
        if current_stage == 'stage6':
            st.markdown("🔄 Current")
        elif stage6_icon == "🟢":
            st.markdown("✅ Complete")
        else:
            st.markdown("⏳ Pending")
    
    with col3:
        stage7_icon = "🟢" if script_executed else "⚪"
        st.markdown(f"{stage7_icon} **Stage 7: Execute**")
        if current_stage == 'stage7':
            st.markdown("🔄 Current")
        elif script_executed:
            st.markdown("✅ Complete")
        else:
            st.markdown("⏳ Pending")
    
    with col4:
        stage4_icon = "🟢" if ready_for_selection else "⚪"
        st.markdown(f"{stage4_icon} **Stage 4: Select**")
        if current_stage == 'stage4' and ready_for_selection:
            st.markdown("🎯 Ready")
        elif current_stage == 'stage4':
            st.markdown("🔄 Current")
        elif ready_for_selection:
            st.markdown("✅ Ready")
        else:
            st.markdown("⏳ Pending")

def render_enhanced_element_selection_ui(state: StateManager) -> bool:
    """
    Render enhanced element selection UI with intelligent browser session continuity.

    This component provides comprehensive browser session status, selection mode options,
    performance indicators, and clear guidance for users.

    Args:
        state: StateManager instance

    Returns:
        bool: True if element selection was initiated
    """
    integration = get_workflow_browser_integration()
    session_info = integration.get_session_info()

    st.markdown("#### 🎯 Enhanced Element Selection")

    # Render browser session status prominently
    _render_session_status_card(session_info)

    if session_info.get('active'):
        # Active session - show enhanced options with detailed information
        return _render_active_session_selection_ui(state, integration, session_info)
    else:
        # No active session - show new session options with clear explanations
        return _render_new_session_selection_ui(state, integration, session_info)


def _render_session_status_card(session_info: Dict[str, Any]) -> None:
    """Render a comprehensive browser session status card."""
    if session_info.get('active'):
        # Active session status
        with st.container():
            col1, col2, col3 = st.columns([2, 1, 1])

            with col1:
                st.success("✅ **Browser Session Active**")
                if session_info.get('current_url'):
                    url_display = session_info['current_url']
                    if len(url_display) > 60:
                        url_display = url_display[:57] + "..."
                    st.caption(f"📍 {url_display}")

                if session_info.get('title'):
                    title_display = session_info['title']
                    if len(title_display) > 50:
                        title_display = title_display[:47] + "..."
                    st.caption(f"📄 {title_display}")

            with col2:
                # Session health indicator
                health_score = session_info.get('health_score', 100)
                if health_score >= 90:
                    st.metric("Session Health", "Excellent", "🟢")
                elif health_score >= 70:
                    st.metric("Session Health", "Good", "🟡")
                else:
                    st.metric("Session Health", "Poor", "🔴")

            with col3:
                # Time remaining
                time_remaining = session_info.get('time_remaining', 0)
                if time_remaining > 0:
                    minutes = int(time_remaining // 60)
                    if minutes > 60:
                        hours = minutes // 60
                        minutes = minutes % 60
                        time_display = f"{hours}h {minutes}m"
                    else:
                        time_display = f"{minutes}m"
                    st.metric("Time Left", time_display, "⏱️")
                else:
                    st.metric("Time Left", "Expired", "⚠️")
    else:
        # Inactive session status
        with st.container():
            col1, col2 = st.columns([3, 1])

            with col1:
                st.error("❌ **No Active Browser Session**")
                error_msg = session_info.get('error', 'Session not initialized')
                st.caption(f"ℹ️ {error_msg}")

            with col2:
                st.metric("Status", "Inactive", "⚪")


def _render_active_session_selection_ui(state: StateManager,
                                      integration: 'WorkflowBrowserIntegration',
                                      session_info: Dict[str, Any]) -> bool:
    """Render element selection UI for active browser sessions."""

    # Performance and continuity benefits
    st.info("🎉 **Browser state preserved!** Element selection will be immediate with full page context.")

    # Show performance benefits using info box instead of expander
    st.info("""
    **💡 Continuity Benefits:**

    **Performance:** ⚡ Instant selection • 🎯 Preserved DOM state • 🔄 No page reload • 📊 Maintained scroll position

    **Context:** 🎮 Interactive elements ready • 🔐 Login state preserved • 📝 Form data maintained • 🎨 Dynamic content visible
    """)

    # Selection options
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        if st.button("🖱️ Select Element (Preserved State)",
                    key="enhanced_element_selection",
                    help="Launch element selector with preserved browser state - instant and context-aware",
                    type="primary",
                    use_container_width=True):
            return True

    with col2:
        if st.button("🔄 Refresh Page",
                    key="refresh_page",
                    help="Refresh the current page while maintaining session",
                    use_container_width=True):
            if hasattr(state, 'website_url') and state.website_url:
                with st.spinner("Refreshing page..."):
                    if integration.session_manager.navigate_to(state.website_url):
                        st.success("✅ Page refreshed successfully")
                        st.rerun()
                    else:
                        st.error("❌ Failed to refresh page")
            else:
                st.warning("⚠️ No website URL available for refresh")

    with col3:
        if st.button("🆕 New Session",
                    key="force_new_session",
                    help="Start a completely new browser session",
                    use_container_width=True):
            with st.spinner("Starting new session..."):
                integration.cleanup_session()
                st.info("🔄 Session reset. Element selection will create a new browser instance.")
                st.rerun()

    return False


def _render_new_session_selection_ui(state: StateManager,
                                   integration: 'WorkflowBrowserIntegration',
                                   session_info: Dict[str, Any]) -> bool:
    """Render element selection UI when no active session exists."""

    # Explanation of what will happen
    st.warning("⚠️ **No active browser session detected**")
    st.info("Element selection will create a new browser instance. This may take a few moments to initialize.")

    # Show what happens with new session using info box instead of expander
    st.info("""
    **ℹ️ New Session Process - What happens when you select an element:**

    1. 🚀 **Browser Launch** - New browser window opens (~3-5 seconds)
    2. 🌐 **Page Navigation** - Navigate to your website URL
    3. 🎯 **Element Selector** - Interactive element selection tool activates
    4. 🖱️ **Your Action** - Click on the element you want to select
    5. ✅ **Completion** - Element data captured and browser closes

    **Estimated time:** 10-30 seconds depending on page load speed
    """)

    # Performance impact indicator
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("**Performance Impact:**")
        st.caption("🐌 Slower than preserved sessions")
        st.caption("🔄 Full page reload required")
        st.caption("⏳ Browser initialization time")

    with col2:
        st.markdown("**Estimated Time:**")
        st.metric("Setup Time", "10-30s", "⏱️")

    # Selection button with clear expectations
    if st.button("🖱️ Select Element (New Browser)",
                key="standard_element_selection",
                help="Launch element selector with new browser instance - will take longer but works reliably",
                type="primary",
                use_container_width=True):
        return True

    # Option to initialize session for future use
    if hasattr(state, 'website_url') and state.website_url:
        st.markdown("---")
        st.markdown("**💡 Tip:** Initialize a browser session for faster future selections")

        if st.button("🚀 Initialize Browser Session",
                    key="init_session",
                    help="Start a browser session that will be preserved for faster element selection",
                    use_container_width=True):
            with st.spinner("Initializing browser session..."):
                if integration.initialize_session(state, headless=False):
                    st.success("✅ Browser session initialized! Future element selections will be faster.")
                    st.rerun()
                else:
                    st.error("❌ Failed to initialize browser session")

    return False

def render_script_execution_continuity_ui(state: StateManager) -> Dict[str, Any]:
    """
    Render script execution UI with continuity options.
    
    Args:
        state: StateManager instance
        
    Returns:
        Dict containing execution options and settings
    """
    st.markdown("#### 🧪 Script Execution with Continuity")
    
    integration = get_workflow_browser_integration()
    session_info = integration.get_session_info()
    
    execution_options = {
        "preserve_session": False,
        "execute_clicked": False,
        "execution_mode": "standard"
    }
    
    # Continuity options
    if session_info.get('active'):
        st.success("✅ Browser session active - continuity available")
        
        execution_options["preserve_session"] = st.checkbox(
            "🔄 Preserve browser session for next stage",
            value=True,
            help="Keep browser session active after script execution for seamless element selection"
        )
        
        if execution_options["preserve_session"]:
            st.info("💡 Browser state will be preserved for immediate element selection in Stage 4")
            execution_options["execution_mode"] = "continuity"
    
    else:
        st.info("ℹ️ No active session - standard execution mode")
        execution_options["execution_mode"] = "standard"
    
    # Execution button
    if st.button("▶️ Execute Script", key="execute_with_continuity"):
        execution_options["execute_clicked"] = True
    
    return execution_options

def render_session_recovery_ui(error_info: Dict[str, Any]) -> Optional[str]:
    """
    Render enhanced session recovery UI when errors occur.

    Provides comprehensive error information, diagnostic details, and multiple
    recovery options with clear explanations of each approach.

    Args:
        error_info: Error information and recovery options

    Returns:
        Optional[str]: Selected recovery action
    """
    # Error header with severity indicator
    error_type = error_info.get('error_type', 'Unknown')
    error_severity = _determine_error_severity(error_type)

    if error_severity == 'critical':
        st.error("🚨 **Critical Browser Session Error**")
    elif error_severity == 'warning':
        st.warning("⚠️ **Browser Session Warning**")
    else:
        st.info("ℹ️ **Browser Session Issue**")

    # Error details
    error_message = error_info.get('error_message', 'Unknown error occurred')
    st.markdown(f"**Error Details:** {error_message}")

    # Show diagnostic information if available
    if error_info.get('diagnostic_info'):
        with st.expander("🔍 Diagnostic Information", expanded=False):
            diagnostic = error_info['diagnostic_info']

            col1, col2 = st.columns(2)
            with col1:
                st.markdown("**Session State:**")
                st.code(f"""
Session ID: {diagnostic.get('session_id', 'N/A')}
Browser Process: {diagnostic.get('browser_alive', 'Unknown')}
Driver Status: {diagnostic.get('driver_status', 'Unknown')}
Last Activity: {diagnostic.get('last_activity', 'N/A')}
                """)

            with col2:
                st.markdown("**Error Context:**")
                st.code(f"""
Error Time: {diagnostic.get('error_time', 'N/A')}
Operation: {diagnostic.get('failed_operation', 'N/A')}
Retry Count: {diagnostic.get('retry_count', 0)}
Recovery Possible: {diagnostic.get('recoverable', 'Unknown')}
                """)

    # Recovery options with detailed explanations
    st.markdown("#### 🔧 Recovery Options")

    recovery_actions = error_info.get('recovery_actions', [])

    # Create recovery option cards
    for action in recovery_actions:
        if action == "Restart browser session":
            _render_recovery_option_card(
                "🔄 Restart Session",
                "Start a fresh browser session",
                "Recommended for most session errors. Creates a new browser instance with clean state.",
                "restart_session",
                "primary"
            )

        elif action == "Continue without session continuity":
            _render_recovery_option_card(
                "➡️ Continue Standard Mode",
                "Proceed without browser continuity",
                "Use standard element selection. Slower but reliable. Good fallback option.",
                "continue_standard",
                "secondary"
            )

        elif action == "Return to previous stage":
            _render_recovery_option_card(
                "⬅️ Return to Previous Stage",
                "Go back to fix the issue",
                "Return to Stage 7 or 6 to regenerate/re-execute script with different settings.",
                "go_back",
                "secondary"
            )

        elif action == "Retry with diagnostics":
            _render_recovery_option_card(
                "🔍 Retry with Diagnostics",
                "Attempt recovery with detailed logging",
                "Try to recover the session with enhanced error reporting for troubleshooting.",
                "retry_diagnostics",
                "secondary"
            )

    # Advanced options
    with st.expander("🛠️ Advanced Recovery Options", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🧹 Clear All Sessions",
                        key="clear_all_sessions",
                        help="Force cleanup of all browser sessions and start fresh"):
                return "clear_all_sessions"

        with col2:
            if st.button("📊 Generate Error Report",
                        key="generate_error_report",
                        help="Create detailed error report for troubleshooting"):
                return "generate_error_report"

    return None


def _determine_error_severity(error_type: str) -> str:
    """Determine error severity based on error type."""
    critical_errors = ['WebDriverException', 'SessionNotCreatedException', 'InvalidSessionIdException']
    warning_errors = ['TimeoutException', 'NoSuchElementException', 'StaleElementReferenceException']

    if error_type in critical_errors:
        return 'critical'
    elif error_type in warning_errors:
        return 'warning'
    else:
        return 'info'


def _render_recovery_option_card(title: str, subtitle: str, description: str,
                                action_key: str, button_type: str = "secondary") -> Optional[str]:
    """Render a recovery option as a card with detailed information."""
    with st.container():
        col1, col2 = st.columns([3, 1])

        with col1:
            st.markdown(f"**{title}**")
            st.caption(subtitle)
            st.markdown(f"*{description}*")

        with col2:
            if st.button("Select",
                        key=f"recovery_{action_key}",
                        type=button_type,
                        use_container_width=True):
                return action_key

    st.markdown("---")
    return None

def render_continuity_settings() -> Dict[str, Any]:
    """
    Render browser continuity settings panel.
    
    Returns:
        Dict containing continuity settings
    """
    st.markdown("#### ⚙️ Browser Continuity Settings")
    
    settings = {}
    
    # Session timeout setting
    settings["session_timeout"] = st.slider(
        "Session Timeout (minutes)",
        min_value=5,
        max_value=120,
        value=30,
        step=5,
        help="How long to keep browser sessions active"
    )
    
    # Headless mode setting
    settings["headless_mode"] = st.checkbox(
        "Headless Mode",
        value=False,
        help="Run browser in background (no visible window)"
    )
    
    # Auto-cleanup setting
    settings["auto_cleanup"] = st.checkbox(
        "Auto-cleanup on workflow completion",
        value=True,
        help="Automatically clean up browser sessions when workflow is complete"
    )
    
    # Performance monitoring
    settings["performance_monitoring"] = st.checkbox(
        "Enable performance monitoring",
        value=True,
        help="Monitor browser performance during continuity sessions"
    )
    
    return settings

def render_session_validation_ui(state: StateManager) -> Dict[str, Any]:
    """
    Render session validation UI to check browser session health before element selection.

    Args:
        state: StateManager instance

    Returns:
        Dict containing validation results and recommendations
    """
    st.markdown("#### 🔍 Session Validation")

    integration = get_workflow_browser_integration()
    session_info = integration.get_session_info()

    validation_results = {
        "session_healthy": False,
        "recommendations": [],
        "warnings": [],
        "can_proceed": False
    }

    if not session_info.get('active'):
        st.warning("⚠️ No active session to validate")
        validation_results["recommendations"].append("Initialize a new browser session")
        return validation_results

    # Perform validation checks
    with st.spinner("Validating browser session..."):
        checks = _perform_session_health_checks(integration, session_info)

    # Display validation results
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("**Session Health Checks:**")
        for check_name, result in checks.items():
            if result['status'] == 'pass':
                st.success(f"✅ {check_name}")
            elif result['status'] == 'warning':
                st.warning(f"⚠️ {check_name}")
                validation_results["warnings"].append(result['message'])
            else:
                st.error(f"❌ {check_name}")
                validation_results["recommendations"].append(result['message'])

    with col2:
        # Overall health score
        health_score = sum(1 for check in checks.values() if check['status'] == 'pass') / len(checks) * 100

        if health_score >= 90:
            st.success(f"🟢 **Excellent** ({health_score:.0f}%)")
            validation_results["session_healthy"] = True
            validation_results["can_proceed"] = True
        elif health_score >= 70:
            st.warning(f"🟡 **Good** ({health_score:.0f}%)")
            validation_results["session_healthy"] = True
            validation_results["can_proceed"] = True
        else:
            st.error(f"🔴 **Poor** ({health_score:.0f}%)")
            validation_results["recommendations"].append("Consider restarting the browser session")

    # Show recommendations if any
    if validation_results["recommendations"]:
        st.markdown("**Recommendations:**")
        for rec in validation_results["recommendations"]:
            st.info(f"💡 {rec}")

    return validation_results


def _perform_session_health_checks(integration: 'WorkflowBrowserIntegration',
                                 session_info: Dict[str, Any]) -> Dict[str, Dict[str, str]]:
    """Perform comprehensive session health checks."""
    checks = {}

    # Check 1: Browser process alive
    try:
        if integration.session_manager and integration.session_manager.driver:
            integration.session_manager.driver.current_url
            checks["Browser Process"] = {"status": "pass", "message": "Browser is responsive"}
        else:
            checks["Browser Process"] = {"status": "fail", "message": "Browser process not accessible"}
    except Exception as e:
        checks["Browser Process"] = {"status": "fail", "message": f"Browser not responding: {str(e)[:50]}"}

    # Check 2: Session timeout
    time_remaining = session_info.get('time_remaining', 0)
    if time_remaining > 1800:  # 30 minutes
        checks["Session Timeout"] = {"status": "pass", "message": "Plenty of time remaining"}
    elif time_remaining > 300:  # 5 minutes
        checks["Session Timeout"] = {"status": "warning", "message": "Session will expire soon"}
    else:
        checks["Session Timeout"] = {"status": "fail", "message": "Session expired or expiring very soon"}

    # Check 3: Page accessibility
    try:
        current_url = session_info.get('current_url', '')
        if current_url and current_url != 'about:blank':
            checks["Page Loaded"] = {"status": "pass", "message": "Page is loaded and accessible"}
        else:
            checks["Page Loaded"] = {"status": "warning", "message": "No page loaded or blank page"}
    except:
        checks["Page Loaded"] = {"status": "fail", "message": "Cannot access current page"}

    # Check 4: Element selection readiness
    try:
        if integration.session_manager and hasattr(integration.session_manager, 'prepare_for_element_selection'):
            # This is a lightweight check - doesn't actually prepare, just validates capability
            checks["Element Selection Ready"] = {"status": "pass", "message": "Ready for element selection"}
        else:
            checks["Element Selection Ready"] = {"status": "fail", "message": "Element selection not available"}
    except:
        checks["Element Selection Ready"] = {"status": "warning", "message": "Element selection readiness uncertain"}

    return checks


def render_session_performance_metrics(session_info: Dict[str, Any]) -> None:
    """Render session performance metrics and monitoring information."""
    st.markdown("#### 📊 Session Performance")

    if not session_info.get('active'):
        st.info("No active session to monitor")
        return

    # Performance metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        memory_usage = session_info.get('memory_usage_mb', 0)
        if memory_usage > 0:
            if memory_usage < 500:
                st.metric("Memory Usage", f"{memory_usage:.0f} MB", "🟢")
            elif memory_usage < 1000:
                st.metric("Memory Usage", f"{memory_usage:.0f} MB", "🟡")
            else:
                st.metric("Memory Usage", f"{memory_usage:.0f} MB", "🔴")
        else:
            st.metric("Memory Usage", "Unknown", "⚪")

    with col2:
        cpu_usage = session_info.get('cpu_usage_percent', 0)
        if cpu_usage > 0:
            if cpu_usage < 30:
                st.metric("CPU Usage", f"{cpu_usage:.1f}%", "🟢")
            elif cpu_usage < 60:
                st.metric("CPU Usage", f"{cpu_usage:.1f}%", "🟡")
            else:
                st.metric("CPU Usage", f"{cpu_usage:.1f}%", "🔴")
        else:
            st.metric("CPU Usage", "Unknown", "⚪")

    with col3:
        session_age = session_info.get('session_age_minutes', 0)
        if session_age > 0:
            if session_age < 15:
                st.metric("Session Age", f"{session_age:.0f}m", "🟢")
            elif session_age < 45:
                st.metric("Session Age", f"{session_age:.0f}m", "🟡")
            else:
                st.metric("Session Age", f"{session_age:.0f}m", "🔴")
        else:
            st.metric("Session Age", "Unknown", "⚪")

    with col4:
        page_load_time = session_info.get('last_page_load_time', 0)
        if page_load_time > 0:
            if page_load_time < 3:
                st.metric("Page Load", f"{page_load_time:.1f}s", "🟢")
            elif page_load_time < 10:
                st.metric("Page Load", f"{page_load_time:.1f}s", "🟡")
            else:
                st.metric("Page Load", f"{page_load_time:.1f}s", "🔴")
        else:
            st.metric("Page Load", "Unknown", "⚪")


def show_continuity_help() -> None:
    """Show comprehensive help information about browser continuity features."""
    st.markdown("#### 📚 Browser Continuity Guide")

    # Create tabs for different help sections
    tab1, tab2, tab3, tab4 = st.tabs(["Overview", "Benefits", "Workflow", "Troubleshooting"])

    with tab1:
        st.markdown("""
        **Browser State Continuity** maintains your browser session across workflow stages,
        enabling seamless element selection without losing page context.

        **Key Concepts:**
        - 🔄 **Session Persistence**: Browser stays open between stages
        - 🎯 **State Preservation**: DOM, cookies, and page context maintained
        - ⚡ **Performance**: Faster element selection with no setup time
        - 🎮 **User Experience**: Smooth workflow transitions
        """)

    with tab2:
        col1, col2 = st.columns(2)
        with col1:
            st.markdown("""
            **Performance Benefits:**
            - ⚡ **10x Faster**: Element selection in 1-2 seconds vs 10-30 seconds
            - 🚀 **No Browser Launch**: Skip browser initialization
            - 🌐 **No Page Load**: Skip website navigation
            - 🎯 **Instant Selection**: Direct element interaction
            """)
        with col2:
            st.markdown("""
            **Context Benefits:**
            - 🔐 **Login State**: Stay logged in across stages
            - 📝 **Form Data**: Preserve filled forms
            - 🎨 **Dynamic Content**: Keep loaded content
            - 📊 **Scroll Position**: Maintain page position
            """)

    with tab3:
        st.markdown("""
        **Workflow Integration:**

        1. **Stage 6 (Generate)**: Script generation with session awareness
        2. **Stage 7 (Execute)**: Script execution while preserving browser state
        3. **Stage 4 (Select)**: Element selection with preserved context

        **Session Lifecycle:**
        - 🚀 **Initialize**: Create browser session (manual or automatic)
        - 🔄 **Preserve**: Maintain session during script execution
        - 🎯 **Utilize**: Use preserved state for element selection
        - 🧹 **Cleanup**: Automatic cleanup on completion or timeout
        """)

    with tab4:
        st.markdown("""
        **Common Issues & Solutions:**

        **Session Not Active:**
        - ✅ Initialize session manually before element selection
        - ✅ Check if session expired (default: 30 minutes)
        - ✅ Verify browser process is running

        **Element Selection Fails:**
        - ✅ Validate session health before selection
        - ✅ Refresh page if content changed
        - ✅ Fall back to new browser instance

        **Performance Issues:**
        - ✅ Monitor memory usage (restart if >1GB)
        - ✅ Check CPU usage (restart if consistently >60%)
        - ✅ Consider session age (restart if >1 hour)
        """)

    # Quick action buttons
    st.markdown("---")
    col1, col2, col3 = st.columns(3)

    with col1:
        if st.button("🚀 Initialize Session", key="help_init_session"):
            st.info("Use the 'Initialize Browser Session' button in the element selection area")

    with col2:
        if st.button("🔍 Validate Session", key="help_validate_session"):
            st.info("Session validation is performed automatically before element selection")

    with col3:
        if st.button("📊 View Metrics", key="help_view_metrics"):
            st.info("Performance metrics are shown in the browser continuity sidebar section")
