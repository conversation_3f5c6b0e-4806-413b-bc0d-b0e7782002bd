#!/usr/bin/env python3
"""
Enhanced Element Selection Test Script for GretahAI ScriptWeaver

This script tests the enhanced element selection functionality with intelligent
browser session continuity, comprehensive status indicators, and error recovery.

Features tested:
- Browser session detection and validation
- Enhanced element selection UI components
- Session performance metrics
- Error handling and recovery
- Graceful fallback mechanisms

Usage:
    streamlit run test_enhanced_element_selection.py
"""

import streamlit as st
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("EnhancedElementSelectionTest")

# Import test components
try:
    from core.workflow_integration import (
        get_workflow_browser_integration,
        initialize_browser_continuity,
        cleanup_browser_continuity
    )
    from ui_components.browser_continuity_components import (
        render_browser_continuity_status,
        render_enhanced_element_selection_ui,
        render_session_validation_ui,
        render_session_performance_metrics,
        render_session_recovery_ui,
        show_continuity_help
    )
    from state_manager import StateManager
    
    IMPORTS_AVAILABLE = True
except ImportError as e:
    st.error(f"❌ Import error: {e}")
    IMPORTS_AVAILABLE = False

def main():
    """Main test application."""
    st.set_page_config(
        page_title="Enhanced Element Selection Test",
        page_icon="🎯",
        layout="wide"
    )
    
    st.title("🎯 Enhanced Element Selection Test")
    st.markdown("Testing intelligent browser session continuity for element selection")
    
    if not IMPORTS_AVAILABLE:
        st.error("Required modules not available. Please check imports.")
        return
    
    # Initialize session state
    if 'test_state' not in st.session_state:
        st.session_state.test_state = 'initialized'
        st.session_state.test_results = []
        st.session_state.website_url = "https://example.com"
    
    # Sidebar for test controls
    render_test_sidebar()
    
    # Main test interface
    render_main_test_interface()

def render_test_sidebar():
    """Render test control sidebar."""
    st.sidebar.title("🧪 Test Controls")
    
    # Test website URL
    st.session_state.website_url = st.sidebar.text_input(
        "Test Website URL",
        value=st.session_state.website_url,
        help="URL to use for testing element selection"
    )
    
    # Test mode selection
    test_mode = st.sidebar.selectbox(
        "Test Mode",
        ["Full Integration Test", "UI Components Only", "Session Management", "Error Scenarios"],
        help="Select which aspects to test"
    )
    
    # Browser continuity status
    if IMPORTS_AVAILABLE:
        render_browser_continuity_status()
    
    # Test actions
    st.sidebar.markdown("### 🎮 Test Actions")
    
    if st.sidebar.button("🚀 Initialize Session"):
        test_initialize_session()
    
    if st.sidebar.button("🧹 Cleanup Session"):
        test_cleanup_session()
    
    if st.sidebar.button("🔄 Reset Tests"):
        reset_test_state()
    
    # Test results summary
    render_test_results_summary()

def render_main_test_interface():
    """Render main test interface."""
    
    # Create tabs for different test areas
    tab1, tab2, tab3, tab4 = st.tabs([
        "🎯 Element Selection", 
        "📊 Session Metrics", 
        "🔧 Error Recovery", 
        "📚 Help & Documentation"
    ])
    
    with tab1:
        render_element_selection_tests()
    
    with tab2:
        render_session_metrics_tests()
    
    with tab3:
        render_error_recovery_tests()
    
    with tab4:
        render_help_documentation()

def render_element_selection_tests():
    """Render element selection test interface."""
    st.markdown("## 🎯 Enhanced Element Selection Tests")
    
    # Mock state manager for testing
    mock_state = create_mock_state_manager()
    
    # Test enhanced element selection UI
    st.markdown("### Enhanced Element Selection UI")
    
    try:
        selection_initiated = render_enhanced_element_selection_ui(mock_state)
        
        if selection_initiated:
            st.success("✅ Element selection initiated!")
            add_test_result("Element Selection UI", "PASS", "UI rendered and selection initiated")
        else:
            st.info("ℹ️ Element selection UI rendered (no selection initiated)")
            add_test_result("Element Selection UI", "PASS", "UI rendered successfully")
            
    except Exception as e:
        st.error(f"❌ Error testing element selection UI: {e}")
        add_test_result("Element Selection UI", "FAIL", str(e))
    
    # Test session validation
    st.markdown("### Session Validation")
    
    try:
        validation_results = render_session_validation_ui(mock_state)
        
        if validation_results:
            st.json(validation_results)
            add_test_result("Session Validation", "PASS", "Validation completed")
        else:
            st.warning("⚠️ No validation results returned")
            add_test_result("Session Validation", "WARN", "No validation results")
            
    except Exception as e:
        st.error(f"❌ Error testing session validation: {e}")
        add_test_result("Session Validation", "FAIL", str(e))

def render_session_metrics_tests():
    """Render session metrics test interface."""
    st.markdown("## 📊 Session Performance Metrics Tests")
    
    # Get session information
    integration = get_workflow_browser_integration()
    session_info = integration.get_session_info()
    
    # Display current session info
    st.markdown("### Current Session Information")
    st.json(session_info)
    
    # Test performance metrics rendering
    st.markdown("### Performance Metrics Rendering")
    
    try:
        render_session_performance_metrics(session_info)
        add_test_result("Performance Metrics", "PASS", "Metrics rendered successfully")
    except Exception as e:
        st.error(f"❌ Error rendering performance metrics: {e}")
        add_test_result("Performance Metrics", "FAIL", str(e))
    
    # Session health analysis
    st.markdown("### Session Health Analysis")
    
    health_score = session_info.get('health_score', 0)
    if health_score >= 90:
        st.success(f"🟢 Excellent session health: {health_score}%")
    elif health_score >= 70:
        st.warning(f"🟡 Good session health: {health_score}%")
    elif health_score > 0:
        st.error(f"🔴 Poor session health: {health_score}%")
    else:
        st.info("⚪ No active session to analyze")

def render_error_recovery_tests():
    """Render error recovery test interface."""
    st.markdown("## 🔧 Error Recovery Tests")
    
    # Simulate different error scenarios
    error_scenarios = {
        "Session Timeout": {
            'error_type': 'TimeoutException',
            'error_message': 'Browser session has timed out',
            'recovery_actions': ['Restart browser session', 'Continue without session continuity'],
            'diagnostic_info': {
                'session_id': 'test-session-123',
                'browser_alive': False,
                'driver_status': 'Disconnected',
                'last_activity': '2024-01-01T12:00:00',
                'error_time': datetime.now().isoformat(),
                'failed_operation': 'Element selection',
                'retry_count': 1,
                'recoverable': True
            }
        },
        "WebDriver Error": {
            'error_type': 'WebDriverException',
            'error_message': 'Chrome browser process crashed unexpectedly',
            'recovery_actions': ['Restart browser session', 'Return to previous stage'],
            'diagnostic_info': {
                'session_id': 'test-session-456',
                'browser_alive': False,
                'driver_status': 'Crashed',
                'last_activity': '2024-01-01T11:55:00',
                'error_time': datetime.now().isoformat(),
                'failed_operation': 'Browser navigation',
                'retry_count': 0,
                'recoverable': True
            }
        }
    }
    
    # Test error recovery UI
    selected_scenario = st.selectbox(
        "Select Error Scenario",
        list(error_scenarios.keys()),
        help="Choose an error scenario to test recovery UI"
    )
    
    if st.button("🧪 Test Error Recovery UI"):
        try:
            error_info = error_scenarios[selected_scenario]
            recovery_action = render_session_recovery_ui(error_info)
            
            if recovery_action:
                st.success(f"✅ Recovery action selected: {recovery_action}")
                add_test_result("Error Recovery UI", "PASS", f"Action: {recovery_action}")
            else:
                st.info("ℹ️ Error recovery UI rendered (no action selected)")
                add_test_result("Error Recovery UI", "PASS", "UI rendered successfully")
                
        except Exception as e:
            st.error(f"❌ Error testing recovery UI: {e}")
            add_test_result("Error Recovery UI", "FAIL", str(e))

def render_help_documentation():
    """Render help and documentation tests."""
    st.markdown("## 📚 Help & Documentation Tests")
    
    # Test help component
    try:
        show_continuity_help()
        add_test_result("Help Documentation", "PASS", "Help content rendered successfully")
    except Exception as e:
        st.error(f"❌ Error rendering help: {e}")
        add_test_result("Help Documentation", "FAIL", str(e))

def create_mock_state_manager():
    """Create a mock state manager for testing."""
    class MockStateManager:
        def __init__(self):
            self.website_url = st.session_state.website_url
            self.current_stage = "STAGE4_DETECT"
    
    return MockStateManager()

def test_initialize_session():
    """Test session initialization."""
    try:
        mock_state = create_mock_state_manager()
        success = initialize_browser_continuity(mock_state, headless=False)
        
        if success:
            st.sidebar.success("✅ Session initialized")
            add_test_result("Session Initialization", "PASS", "Session created successfully")
        else:
            st.sidebar.error("❌ Session initialization failed")
            add_test_result("Session Initialization", "FAIL", "Failed to create session")
            
    except Exception as e:
        st.sidebar.error(f"❌ Error: {e}")
        add_test_result("Session Initialization", "FAIL", str(e))

def test_cleanup_session():
    """Test session cleanup."""
    try:
        cleanup_browser_continuity()
        st.sidebar.success("✅ Session cleaned up")
        add_test_result("Session Cleanup", "PASS", "Session cleaned up successfully")
    except Exception as e:
        st.sidebar.error(f"❌ Error: {e}")
        add_test_result("Session Cleanup", "FAIL", str(e))

def reset_test_state():
    """Reset test state."""
    st.session_state.test_state = 'reset'
    st.session_state.test_results = []
    st.sidebar.success("✅ Test state reset")

def add_test_result(test_name: str, status: str, message: str):
    """Add a test result to the session state."""
    result = {
        'timestamp': datetime.now().isoformat(),
        'test_name': test_name,
        'status': status,
        'message': message
    }
    st.session_state.test_results.append(result)

def render_test_results_summary():
    """Render test results summary in sidebar."""
    if st.session_state.test_results:
        st.sidebar.markdown("### 📊 Test Results")
        
        total_tests = len(st.session_state.test_results)
        passed_tests = len([r for r in st.session_state.test_results if r['status'] == 'PASS'])
        failed_tests = len([r for r in st.session_state.test_results if r['status'] == 'FAIL'])
        
        st.sidebar.metric("Total Tests", total_tests)
        st.sidebar.metric("Passed", passed_tests, delta=None)
        st.sidebar.metric("Failed", failed_tests, delta=None)
        
        # Show recent results
        if st.sidebar.button("📋 Show Detailed Results"):
            st.sidebar.json(st.session_state.test_results[-5:])  # Show last 5 results

if __name__ == "__main__":
    main()
